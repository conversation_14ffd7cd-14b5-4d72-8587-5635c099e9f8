using Godot;
using System;

public partial class SeedMove : Node2D
{
	[Export] public Texture2D SeedsTexture { get; set; }
	
	private Sprite2D _sprite;
	private Tween _moveTween;
	private Vector2 _sourcePosition;
	private Vector2 _destinationPosition;
	private PlantType _plantType;
	private int _hoeLevel;
	
	// Animation settings
	private const float MOVE_DURATION = 0.8f;
	private const float ARC_HEIGHT = 32.0f;
	
	[Signal]
	public delegate void SeedPlantedEventHandler(Vector2 tilePosition, PlantType plantType, int hoeLevel);
	
	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		
		if (SeedsTexture == null)
		{
			SeedsTexture = GD.Load<Texture2D>("res://resources/solaria/planting/seeds.png");
		}
		
		if (_sprite != null)
		{
			_sprite.Texture = SeedsTexture;
		}
	}
	
	/// <summary>
	/// Initialize the seed movement with source, destination, and plant type
	/// </summary>
	public void Initialize(Vector2 sourcePosition, Vector2 destinationPosition, PlantType plantType, int hoeLevel)
	{
		_sourcePosition = sourcePosition;
		_destinationPosition = destinationPosition;
		_plantType = plantType;
		_hoeLevel = hoeLevel;
		
		GlobalPosition = _sourcePosition;
		
		// Start the movement animation
		StartMovement();
	}
	
	private void StartMovement()
	{
		if (_moveTween != null)
		{
			_moveTween.Kill();
		}
		
		_moveTween = CreateTween();
		_moveTween.SetParallel(true);
		
		// Create an arc movement by animating X and Y separately
		// X movement: linear from source to destination
		_moveTween.TweenMethod(Callable.From<float>(UpdateXPosition), _sourcePosition.X, _destinationPosition.X, MOVE_DURATION);
		
		// Y movement: arc trajectory
		_moveTween.TweenMethod(Callable.From<float>(UpdateYPosition), 0.0f, 1.0f, MOVE_DURATION);
		
		// Scale animation for visual effect
		_moveTween.TweenProperty(_sprite, "scale", Vector2.One * 0.8f, MOVE_DURATION * 0.5f);
		_moveTween.TweenProperty(_sprite, "scale", Vector2.One, MOVE_DURATION * 0.5f).SetDelay(MOVE_DURATION * 0.5f);
		
		// Rotation for visual effect
		_moveTween.TweenProperty(_sprite, "rotation_degrees", 360.0f, MOVE_DURATION);
		
		// When movement completes, plant the seed
		_moveTween.TweenCallback(Callable.From(OnMovementComplete)).SetDelay(MOVE_DURATION);
	}
	
	private void UpdateXPosition(float x)
	{
		GlobalPosition = new Vector2(x, GlobalPosition.Y);
	}
	
	private void UpdateYPosition(float progress)
	{
		// Create arc trajectory using quadratic bezier curve
		float y = Mathf.Lerp(_sourcePosition.Y, _destinationPosition.Y, progress);
		
		// Add arc height (parabolic curve)
		float arcOffset = 4 * ARC_HEIGHT * progress * (1 - progress);
		y -= arcOffset;
		
		GlobalPosition = new Vector2(GlobalPosition.X, y);
	}
	
	private void OnMovementComplete()
	{
		// Emit signal to notify that the seed has been planted
		Vector2I tilePosition = new Vector2I(
			Mathf.FloorToInt(_destinationPosition.X / 16),
			Mathf.FloorToInt(_destinationPosition.Y / 16)
		);
		
		EmitSignal(SignalName.SeedPlanted, _destinationPosition, (int)_plantType, _hoeLevel);
		
		// Remove the seed sprite
		QueueFree();
	}
	
	public override void _ExitTree()
	{
		if (_moveTween != null)
		{
			_moveTween.Kill();
		}
	}
}
