using Godot;

[System.Serializable]
public enum ResourceType
{
	None = 0,
	<PERSON> = 1,
	<PERSON> = 2,
	Net = 3,
	<PERSON>k = 4,
	<PERSON><PERSON> = 5,
	<PERSON> = 6,
	<PERSON> = 7,
	<PERSON> = 8,

	CopperOre = 9,
	IronOre = 10,
	GoldOre = 11,
	IndigosiumOre = 12,
	MithrilOre = 13,
	ErithrydiumOre = 14,
	AdamantiteOre = 15,
	UraniumOre = 16,

	CopperBar = 17,
	IronBar = 18,
	GoldBar = 19,
	IndigosiumBar = 20,
	MithrilBar = 21,
	ErithrydiumBar = 22,
	AdamantiteBar = 23,
	UraniumBar = 24,

	CopperSheet = 25,
	IronSheet = 26,
	GoldSheet = 27,
	IndigosiumSheet = 28,
	MithrilSheet = 29,
	ErithrydiumSheet = 30,
	AdamantiteSheet = 31,
	UraniumSheet = 32,

	WoodenBeam = 33,
	WoodenStick = 34,
	RawRabbitLeg = 35,
	CookedRabbitLeg = 36,
	<PERSON>enKey = 37,
	Arrow = 38,
	CopperKey = 39,
	IronKey = 40,
	<PERSON><PERSON><PERSON> = 41,
	IndigosiumKey = 42,
	Mith<PERSON><PERSON><PERSON> = 43,
	<PERSON><PERSON><PERSON><PERSON><PERSON>ey = 44,
	AdamantiteKey = 45,
	UraniumKey = 46,
	Charcoal = 47,

	BrownMushroom = 48,
	BlueMushroom = 49,
	RedMushroom = 50,
	VioletMushroom = 51,

	// Plants (52-99) - ordered to match sprite sheet rows
	Carrot = 52,
	Turnip = 53,
	Pumpkin = 54,
	Potato = 55,
	Onion = 56,
	Strawberry = 57,
	Cauliflower = 58,
	Tomato = 59,
	Parsnip = 60,
	SnapPeas = 61,
	Garlic = 62,
	Radish = 63,
	Corn = 64,
	Leek = 65,
	Wheat = 66,
	Sunflower = 67,
	Beetroot = 68,
	Cabbage = 69,
	RedCabbage = 70,
	Broccoli = 71,
	BrusselsSprout = 72,
	RedBellPepper = 73,
	Spinach = 74,
	BokChoy = 75,
	Artichoke = 76,
	Cotton = 77,
	PurpleGrapes = 78,
	GreenGrapes = 79,
	RedGrapes = 80,
	PinkGrapes = 81,
	Cantaloupe = 82,
	Honeydew = 83,
	ButternutSquash = 84,
	Buckwheat = 85,
	YellowBellPepper = 86,
	OrangeBellPepper = 87,
	PurpleBellPepper = 88,
	WhiteBellPepper = 89,
	Coffee = 90,
	Amaranth = 91,
	GlassGemCorn = 92,
	GreenChilliPepper = 93,
	RedChilliPepper = 94,
	YellowChilliPepper = 95,
	OrangeChilliPepper = 96,
	PurpleChilliPepper = 97,

	// Seed Bags (100-147) - corresponding to plants above
	CarrotSeedBag = 100,
	TurnipSeedBag = 101,
	PumpkinSeedBag = 102,
	PotatoSeedBag = 103,
	OnionSeedBag = 104,
	StrawberrySeedBag = 105,
	CauliflowerSeedBag = 106,
	TomatoSeedBag = 107,
	ParsnipSeedBag = 108,
	SnapPeasSeedBag = 109,
	GarlicSeedBag = 110,
	RadishSeedBag = 111,
	CornSeedBag = 112,
	LeekSeedBag = 113,
	WheatSeedBag = 114,
	SunflowerSeedBag = 115,
	BeetrootSeedBag = 116,
	CabbageSeedBag = 117,
	RedCabbageSeedBag = 118,
	BroccoliSeedBag = 119,
	BrusselsSproutSeedBag = 120,
	RedBellPepperSeedBag = 121,
	SpinachSeedBag = 122,
	BokChoySeedBag = 123,
	ArtichokeSeedBag = 124,
	CottonSeedBag = 125,
	PurpleGrapesSeedBag = 126,
	GreenGrapesSeedBag = 127,
	RedGrapesSeedBag = 128,
	PinkGrapesSeedBag = 129,
	CantaloupeSeedBag = 130,
	HoneydewSeedBag = 131,
	ButternutSquashSeedBag = 132,
	BuckwheatSeedBag = 133,
	YellowBellPepperSeedBag = 134,
	OrangeBellPepperSeedBag = 135,
	PurpleBellPepperSeedBag = 136,
	WhiteBellPepperSeedBag = 137,
	CoffeeSeedBag = 138,
	AmaranthSeedBag = 139,
	GlassGemCornSeedBag = 140,
	GreenChilliPepperSeedBag = 141,
	RedChilliPepperSeedBag = 142,
	YellowChilliPepperSeedBag = 143,
	OrangeChilliPepperSeedBag = 144,
	PurpleChilliPepperSeedBag = 145,

	// New resources added at the end to preserve existing enum values
	StoneBrick = 148,
	Stone2Brick = 149,
	Nails = 150,
	BaseSeed = 151,
	SeedBag = 152
}
