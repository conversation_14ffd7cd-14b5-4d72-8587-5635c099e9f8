[gd_scene load_steps=5 format=3 uid="uid://c0yna3biwkpso"]

[ext_resource type="Script" uid="uid://b8rlfy4isfj82" path="res://scenes/mapObjects/Tree2.cs" id="1_tree2_script"]
[ext_resource type="Texture2D" uid="uid://caadj6akptvcp" path="res://resources/solaria/exterior/tree2.png" id="2_4pp2b"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_tree2_progressbar"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_tree2"]
size = Vector2(6, 6)

[node name="Tree2" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_tree2_script")

[node name="TreeSprite" type="Sprite2D" parent="."]
texture = ExtResource("2_4pp2b")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]
position = Vector2(0, 8)

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 11)
shape = SubResource("RectangleShape2D_tree2")

[node name="ProgressBar" parent="." instance=ExtResource("3_tree2_progressbar")]
position = Vector2(0, 24)
scale = Vector2(1, 0.6)
