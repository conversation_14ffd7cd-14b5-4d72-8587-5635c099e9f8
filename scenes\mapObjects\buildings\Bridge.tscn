[gd_scene load_steps=4 format=3 uid="uid://cqdi31xbx3lmd"]

[ext_resource type="Script" uid="uid://c3vpjj4rhkyq3" path="res://scenes/mapObjects/buildings/Bridge.cs" id="1_bridge"]
[ext_resource type="Texture2D" uid="uid://bsrmiu50aadu8" path="res://resources/solaria/buildings/bridge.png" id="2_bridge_texture"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progress"]

[node name="Bridge" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_bridge")

[node name="BridgeSprite" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -32)
texture = ExtResource("2_bridge_texture")
offset = Vector2(0, 32)

[node name="ProgressBar" parent="." instance=ExtResource("3_progress")]
position = Vector2(0, -12)
scale = Vector2(1, 0.6)
