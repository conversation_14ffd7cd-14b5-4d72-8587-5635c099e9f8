[gd_scene load_steps=8 format=3 uid="uid://fx72jxna3bnp"]

[ext_resource type="Script" uid="uid://ev7yxfq433l4" path="res://scenes/mapObjects/buildings/Workbench.cs" id="1_workbench"]
[ext_resource type="Texture2D" uid="uid://cqymbosx0ly5y" path="res://resources/solaria/buildings/workbench.png" id="2_kparl"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progress_bar"]
[ext_resource type="PackedScene" uid="uid://bjvyoro1j3ud3" path="res://scenes/UI/buildingMenus/WorkbenchMenu.tscn" id="4_workbench_menu"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_upgrading_progress"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_workbench"]
size = Vector2(30, 13)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_flviw"]
size = Vector2(30, 13)

[node name="Workbench" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_workbench")

[node name="WorkbenchSprite" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("2_kparl")

[node name="UpgradingTool" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(1, 18)
scale = Vector2(0.75, 0.75)
offset = Vector2(0, -30)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 1.5)
shape = SubResource("RectangleShape2D_workbench")

[node name="ProgressBar" parent="." instance=ExtResource("3_progress_bar")]
position = Vector2(0.4, 9)
scale = Vector2(1.05, 0.6)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(0, 1.5)
shape = SubResource("RectangleShape2D_flviw")

[node name="WorkbenchMenu" parent="." instance=ExtResource("4_workbench_menu")]

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_upgrading_progress")]
visible = false
z_index = 1
position = Vector2(16, -1)
