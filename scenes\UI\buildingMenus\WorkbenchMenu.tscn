[gd_scene load_steps=35 format=3 uid="uid://bjvyoro1j3ud3"]

[ext_resource type="Texture2D" uid="uid://cyvhsyv6xia6m" path="res://resources/solaria/UI/build/build_panel.png" id="1_7wokg"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="2_64g60"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="4_lvfs3"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="5_6o5jt"]
[ext_resource type="Texture2D" uid="uid://qtnmf3qqfvr5" path="res://resources/solaria/UI/build/buildButton.png" id="6_wrunb"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="7_frmi2"]
[ext_resource type="Texture2D" uid="uid://bdscl0odejahl" path="res://resources/solaria/resources/resource_stone.png" id="7_lvfs3"]
[ext_resource type="Script" uid="uid://dy1q2ocslwlwa" path="res://scenes/UI/buildingMenus/WorkbenchMenu.cs" id="14_workbenchmenu_script"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wltr5"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_bu41f"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_titi1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_addpo"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cfprg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_4bpbu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iq3py"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yklww"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_xf8fr"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_07hwm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_oxiyu"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_mes8s"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sapyx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_b4v27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jlemb"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3sn0u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_kauif"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jurd5"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpeu1"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_48vcc"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_wrunb"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_ayp1v"]

[node name="WorkbenchMenu" type="CanvasLayer"]
script = ExtResource("14_workbenchmenu_script")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
visible = false
scale = Vector2(0.95, 0.95)
texture = ExtResource("1_7wokg")

[node name="ScrollContainer" type="ScrollContainer" parent="Control/Panel"]
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -92.0
offset_top = -116.0
offset_right = -99.0
offset_bottom = -124.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Control/Panel/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="ItemListPickaxe" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_bu41f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(141.32, 18.7369)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 12.8421
offset_right = 206.526
offset_bottom = 29.8421
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(141.32, 32.4211)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 26.5263
offset_right = 206.526
offset_bottom = 43.5263
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 218.0
offset_bottom = 17.0
scale = Vector2(0.63, 0.63)
text = "PICKAXE_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 209.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListPickaxe"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListHammer" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxEmpty_wrunb")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(141.32, 18.9474)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 13.0527
offset_right = 206.526
offset_bottom = 30.0527
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(141.32, 31.579)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 25.6843
offset_right = 206.526
offset_bottom = 42.6843
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 216.0
offset_bottom = 17.0
scale = Vector2(0.63, 0.63)
text = "HAMMER_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 209.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHammer"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListHoe" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_bu41f")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(141.32, 17.0526)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 11.1579
offset_right = 206.526
offset_bottom = 28.1579
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(141.32, 29.8948)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 24.0
offset_right = 206.526
offset_bottom = 41.0
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 216.0
offset_bottom = 18.0
scale = Vector2(0.63, 0.63)
text = "HOE_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 206.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListHoe"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListSword" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxEmpty_wrunb")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(141.32, 18.3158)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 12.4211
offset_right = 206.526
offset_bottom = 29.4211
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(141.32, 30.9473)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 25.0527
offset_right = 206.526
offset_bottom = 42.0527
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 216.0
offset_bottom = 18.0
scale = Vector2(0.63, 0.63)
text = "SWORD_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 206.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListSword"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListBow" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxFlat_ayp1v")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(141.32, 18.5263)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 12.6316
offset_right = 206.526
offset_bottom = 29.6316
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(141.32, 31.1579)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 25.2632
offset_right = 206.526
offset_bottom = 42.2632
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 216.0
offset_bottom = 17.0
scale = Vector2(0.63, 0.63)
text = "BOW_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 206.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListBow"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="ItemListWateringCan" type="ItemList" parent="Control/Panel/ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(0, 40)
layout_mode = 2
theme_override_styles/focus = SubResource("StyleBoxEmpty_wltr5")
theme_override_styles/panel = SubResource("StyleBoxEmpty_wrunb")
theme_override_styles/hovered = SubResource("StyleBoxEmpty_titi1")
theme_override_styles/hovered_selected = SubResource("StyleBoxEmpty_addpo")
theme_override_styles/hovered_selected_focus = SubResource("StyleBoxEmpty_cfprg")
theme_override_styles/selected = SubResource("StyleBoxEmpty_4bpbu")
theme_override_styles/selected_focus = SubResource("StyleBoxEmpty_iq3py")
theme_override_styles/cursor_unfocused = SubResource("StyleBoxEmpty_yklww")
theme_override_styles/cursor = SubResource("StyleBoxEmpty_xf8fr")

[node name="IconBackground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("2_64g60")

[node name="IconForeground" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(17.4737, 19.3684)
texture = ExtResource("4_lvfs3")

[node name="PriceIcon2" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(141.32, 18.7368)
texture = ExtResource("4_lvfs3")

[node name="Price2" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 12.8421
offset_right = 206.526
offset_bottom = 29.8421
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="PriceIcon3" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(141.32, 31.3684)
texture = ExtResource("7_lvfs3")

[node name="Price3" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 122.526
offset_top = 25.4737
offset_right = 206.526
offset_bottom = 42.4737
scale = Vector2(0.73, 0.73)
text = "20"
horizontal_alignment = 0

[node name="BuildButton" type="Sprite2D" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
position = Vector2(165.684, 21.2632)
scale = Vector2(1.49, 1.49)
texture = ExtResource("6_wrunb")

[node name="ItemName" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_right = 216.0
offset_bottom = 17.0
scale = Vector2(0.63, 0.63)
text = "WATERING_CAN_UPGRADE"
horizontal_alignment = 0

[node name="ItemDescription" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan" instance=ExtResource("5_6o5jt")]
layout_mode = 0
offset_left = 34.0
offset_top = 13.0
offset_right = 200.0
offset_bottom = 64.0
scale = Vector2(0.495, 0.495)
text = "test"
horizontal_alignment = 0
vertical_alignment = 0

[node name="Button" type="Button" parent="Control/Panel/ScrollContainer/VBoxContainer/ItemListWateringCan"]
layout_mode = 0
offset_left = 150.0
offset_top = 4.0
offset_right = 181.0
offset_bottom = 36.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")

[node name="Close" type="Sprite2D" parent="Control/Panel"]
position = Vector2(101.263, -125.053)
texture = ExtResource("7_frmi2")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 91.1053
offset_top = -137.474
offset_right = 111.105
offset_bottom = -115.474
theme_override_styles/focus = SubResource("StyleBoxEmpty_07hwm")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_oxiyu")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_mes8s")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_sapyx")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_b4v27")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_jlemb")
theme_override_styles/hover = SubResource("StyleBoxEmpty_3sn0u")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_kauif")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_jurd5")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_jpeu1")
theme_override_styles/normal = SubResource("StyleBoxEmpty_48vcc")
