using Godot;

public partial class BuildingPlacer : Node2D
{
	[Export] public PackedScene AnvilScene { get; set; }
	[Export] public PackedScene BridgeScene { get; set; }
	[Export] public PackedScene CampfireScene { get; set; }
	[Export] public PackedScene Furnace1Scene { get; set; }
	[Export] public PackedScene Furnace2Scene { get; set; }
	[Export] public PackedScene Furnace3Scene { get; set; }
	[Export] public PackedScene Furnace4Scene { get; set; }
	[Export] public PackedScene GrindstoneScene { get; set; }
	[Export] public PackedScene WorkbenchScene { get; set; }
	[Export] public PackedScene SeedMakerScene { get; set; }
	
	[Export] public AudioStream BuildingPlaced { get; set; }
	
	private Node2D _currentBuilding;
	private bool _isPlacingBuilding = false;
	private Camera2D _camera;
	private EffectPlayer _effectPlayer;
	
	public override void _Ready()
	{
		_effectPlayer = GetNode<EffectPlayer>("EffectPlayer");
		_camera = GetNode<Camera2D>("/root/world/Player/Camera2D");
		if (_camera == null)
		{
			GD.PrintErr("BuildingPlacer: Camera2D not found!");
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlacingBuilding || _currentBuilding == null) return;

		if (@event is InputEventMouseButton mouseButton)
		{
			if (mouseButton.Pressed)
			{
				if (mouseButton.ButtonIndex == MouseButton.Left)
				{
					TryPlaceBuilding();
				}
				else if (mouseButton.ButtonIndex == MouseButton.Right)
				{
					CancelBuildingPlacement();
				}
			}
		}
		else if (@event is InputEventMouseMotion)
		{
			UpdateBuildingPosition();
		}
	}

	public void StartPlacingAnvil()
	{
		if (AnvilScene == null)
		{
			GD.PrintErr("BuildingPlacer: AnvilScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = AnvilScene.Instantiate<Anvil>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Anvil!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingBridge()
	{
		if (BridgeScene == null)
		{
			GD.PrintErr("BuildingPlacer: BridgeScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = BridgeScene.Instantiate<Bridge>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Bridge!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingCampfire()
	{
		if (CampfireScene == null)
		{
			GD.PrintErr("BuildingPlacer: CampfireScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = CampfireScene.Instantiate<Campfire>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Campfire!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace1()
	{
		if (Furnace1Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace1Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace1Scene.Instantiate<Furnace1>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace1!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace2()
	{
		if (Furnace2Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace2Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace2Scene.Instantiate<Furnace2>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace2!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace3()
	{
		if (Furnace3Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace3Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace3Scene.Instantiate<Furnace3>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace3!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingFurnace4()
	{
		if (Furnace4Scene == null)
		{
			GD.PrintErr("BuildingPlacer: Furnace4Scene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = Furnace4Scene.Instantiate<Furnace4>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate Furnace4!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingGrindstone()
	{
		if (GrindstoneScene == null)
		{
			GD.PrintErr("BuildingPlacer: GrindstoneScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = GrindstoneScene.Instantiate<Grindstone>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate grindstone!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingWorkbench()
	{
		if (WorkbenchScene == null)
		{
			GD.PrintErr("BuildingPlacer: WorkbenchScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = WorkbenchScene.Instantiate<Workbench>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate workbench!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	public void StartPlacingSeedMaker()
	{
		if (SeedMakerScene == null)
		{
			GD.PrintErr("BuildingPlacer: SeedMakerScene not set!");
			return;
		}

		CancelBuildingPlacement();

		_currentBuilding = SeedMakerScene.Instantiate<SeedMaker>();
		if (_currentBuilding == null)
		{
			GD.PrintErr("BuildingPlacer: Failed to instantiate seed maker!");
			return;
		}

		var buildingsNode = GetNode<Node2D>("/root/world/Buildings");
		if (buildingsNode != null)
		{
			buildingsNode.AddChild(_currentBuilding);
		}
		else
		{
			GetParent().AddChild(_currentBuilding);
		}

		_isPlacingBuilding = true;

		// Disable player movement during building placement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		UpdateBuildingPosition();
	}

	private void UpdateBuildingPosition()
	{
		if (_currentBuilding == null) return;

		Vector2 mousePos = GetGlobalMousePosition();

		int tileX = Mathf.FloorToInt(mousePos.X / 16.0f);
		int tileY = Mathf.FloorToInt(mousePos.Y / 16.0f);
		Vector2I topLeftTile = new Vector2I(tileX, tileY);

		bool canPlace = false;

		if (_currentBuilding is Anvil anvil)
		{
			anvil.SetTilePosition(topLeftTile);
			canPlace = anvil.CanBePlacedAt(topLeftTile);
			anvil.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Bridge bridge)
		{
			bridge.SetTilePosition(topLeftTile);
			canPlace = bridge.CanBePlacedAt(topLeftTile);
			bridge.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Campfire campfire)
		{
			campfire.SetTilePosition(topLeftTile);
			canPlace = campfire.CanBePlacedAt(topLeftTile);
			campfire.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace1 furnace1)
		{
			furnace1.SetTilePosition(topLeftTile);
			canPlace = furnace1.CanBePlacedAt(topLeftTile);
			furnace1.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace2 furnace2)
		{
			furnace2.SetTilePosition(topLeftTile);
			canPlace = furnace2.CanBePlacedAt(topLeftTile);
			furnace2.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace3 furnace3)
		{
			furnace3.SetTilePosition(topLeftTile);
			canPlace = furnace3.CanBePlacedAt(topLeftTile);
			furnace3.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Furnace4 furnace4)
		{
			furnace4.SetTilePosition(topLeftTile);
			canPlace = furnace4.CanBePlacedAt(topLeftTile);
			furnace4.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Grindstone grindstone)
		{
			grindstone.SetTilePosition(topLeftTile);
			canPlace = grindstone.CanBePlacedAt(topLeftTile);
			grindstone.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is Workbench workbench)
		{
			workbench.SetTilePosition(topLeftTile);
			canPlace = workbench.CanBePlacedAt(topLeftTile);
			workbench.SetPlacementFeedback(canPlace);
		}
		else if (_currentBuilding is SeedMaker seedMaker)
		{
			seedMaker.SetTilePosition(topLeftTile);
			canPlace = seedMaker.CanBePlacedAt(topLeftTile);
			seedMaker.SetPlacementFeedback(canPlace);
		}
	}

	private void TryPlaceBuilding()
	{
		if (_currentBuilding == null) return;

		Vector2I topLeftTile = Vector2I.Zero;
		bool canPlace = false;

		if (_currentBuilding is Anvil anvil)
		{
			topLeftTile = anvil.GetTopLeftTilePosition();
			canPlace = anvil.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				anvil.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Bridge bridge)
		{
			topLeftTile = bridge.GetTopLeftTilePosition();
			canPlace = bridge.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				bridge.PlaceBuilding();
				_effectPlayer.Play(BuildingPlaced);
			}
		}
		else if (_currentBuilding is Campfire campfire)
		{
			topLeftTile = campfire.GetTopLeftTilePosition();
			canPlace = campfire.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				campfire.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace1 furnace1)
		{
			topLeftTile = furnace1.GetTopLeftTilePosition();
			canPlace = furnace1.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace1.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace2 furnace2)
		{
			topLeftTile = furnace2.GetTopLeftTilePosition();
			canPlace = furnace2.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace2.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace3 furnace3)
		{
			topLeftTile = furnace3.GetTopLeftTilePosition();
			canPlace = furnace3.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace3.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Furnace4 furnace4)
		{
			topLeftTile = furnace4.GetTopLeftTilePosition();
			canPlace = furnace4.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				furnace4.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Grindstone grindstone)
		{
			topLeftTile = grindstone.GetTopLeftTilePosition();
			canPlace = grindstone.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				grindstone.PlaceBuilding();
			}
		}
		else if (_currentBuilding is Workbench workbench)
		{
			topLeftTile = workbench.GetTopLeftTilePosition();
			canPlace = workbench.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				workbench.PlaceBuilding();
			}
		}
		else if (_currentBuilding is SeedMaker seedMaker)
		{
			topLeftTile = seedMaker.GetTopLeftTilePosition();
			canPlace = seedMaker.CanBePlacedAt(topLeftTile);
			if (canPlace)
			{
				seedMaker.PlaceBuilding();
			}
		}

		if (canPlace)
		{
			_currentBuilding = null;
			_isPlacingBuilding = false;

			// Re-enable player movement when building is placed
			CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
		}
	}

	private void CancelBuildingPlacement()
	{
		if (_currentBuilding != null)
		{
			_currentBuilding.QueueFree();
			_currentBuilding = null;
		}

		_isPlacingBuilding = false;

		// Re-enable player movement when building placement is cancelled
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
	}

	public bool IsPlacingBuilding()
	{
		return _isPlacingBuilding;
	}
}
