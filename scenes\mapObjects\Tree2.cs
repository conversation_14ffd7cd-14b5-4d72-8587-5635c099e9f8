using Godot;
using System;

/// <summary>
/// Tree2 object that can be destroyed by pickaxe
/// Handles hit animations, health management, and resource dropping
/// </summary>
public partial class Tree2 : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 8;
	[Export] public int PickaxeDamage { get; set; } = 2;
	[Export] public ResourceType ResourceType { get; set; } = ResourceType.Wood;
	[Export] public int ResourceAmount { get; set; } = 3;

	// Event for when tree is destroyed
	[Signal] public delegate void Tree2DestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	// Hit animation properties
	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f); // White tint
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f; // 50% white tint

	public override void _Ready()
	{
		_currentHealth = MaxHealth;
		_sprite = GetNode<Sprite2D>("TreeSprite");
		_hpBar = GetNode<ProgressBar>("ProgressBar");

		// Connect to pickaxe usage signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}

		// Initialize HP bar
		UpdateHPBar();
	}

	/// <summary>
	/// Handle damage to the tree
	/// </summary>
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		PlayHitAnimation();

		GD.Print($"Tree2 took {damage} damage! Health: {_currentHealth}/{MaxHealth}");

		if (_currentHealth <= 0)
		{
			DestroyTree();
		}
	}

	/// <summary>
	/// Play hit animation with white tint and scale effects
	/// </summary>
	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		// White tint effect
		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		// Scale effect (smaller then bigger)
		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	/// <summary>
	/// Destroy the tree and drop resources
	/// </summary>
	private void DestroyTree()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Drop resources
		DropResources();

		// Clear tile occupation
		_customDataManager?.ClearObjectPlaced(_tilePosition);

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(5);

		// Emit destruction signal
		EmitSignal(SignalName.Tree2Destroyed, _tilePosition);

		// Remove from scene
		QueueFree();
	}

	/// <summary>
	/// Drop resources when tree is destroyed
	/// </summary>
	private void DropResources()
	{
		// Spawn multiple individual resources (each with quantity 1)
		for (int i = 0; i < ResourceAmount; i++)
		{
			// Add small random offset to prevent resources from stacking exactly
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
				(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
			);
			Vector2 spawnPosition = GlobalPosition + offset;

			DroppedResource.SpawnResource(spawnPosition, ResourceType, 1);
		}
	}

	/// <summary>
	/// Get current health
	/// </summary>
	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Set current health
	/// </summary>
	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	/// <summary>
	/// Get tile position
	/// </summary>
	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	/// <summary>
	/// Set tile position (used by spawner)
	/// </summary>
	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		// Position object in the center of the tile (tile size is 16x16)
		// Visual offset: -16 on Y to make tree appear "on the ground"
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8 - 16);
	}

	/// <summary>
	/// Check if this tree can be hit by pickaxe from given position
	/// </summary>
	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		// Tree can be hit if player is adjacent (including diagonally)
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
