using Godot;
using System;

public partial class InventoryItemDescriptionPanel : Node2D
{
	private Sprite2D _selectedItem;
	private Label _resourceNameLabel;
	private Node2D _descriptionVariant1;
	private Node2D _descriptionVariant2;
	private Label _descriptionLabel;
	
	private Button _useButton;
	private Button _removeButton;
	private Button _quickActionButton;
	private Button _button4;

	private Sprite2D _useButtonSprite;
	private Sprite2D _removeButtonSprite;
	private Sprite2D _quickActionButtonSprite;
	private Sprite2D _button4Sprite;
	
	private Label _useButtonLabel;
	private Label _removeButtonLabel;
	private Label _quickActionButtonLabel;
	private Label _button4Label;
	
	private ResourceType _currentResourceType = ResourceType.Wood;
	private ToolType _currentToolType = ToolType.None;
	private bool _isToolSelected = false;
	private int _currentQuantity = 0;
	


	public override void _Ready()
	{
		InitializeComponents();
		ConnectSignals();
		ClearSelection();
	}

	private void InitializeComponents()
	{
		_selectedItem = GetNode<Sprite2D>("Panel/SelectedItem");
		_resourceNameLabel = GetNode<Label>("Panel/ResourceName");
		_descriptionVariant1 = GetNode<Node2D>("Panel/DescriptionVariant1");
		_descriptionVariant2 = GetNode<Node2D>("Panel/DescriptionVariant2");
		_descriptionLabel = GetNode<Label>("Panel/DescriptionVariant1/ResourceName");
		
		_useButton = GetNode<Button>("Button1/Button");
		_removeButton = GetNode<Button>("Button2/Button");
		_quickActionButton = GetNode<Button>("Button3/Button");
		_button4 = GetNode<Button>("Button4/Button");

		_useButtonSprite = GetNode<Sprite2D>("Button1");
		_removeButtonSprite = GetNode<Sprite2D>("Button2");
		_quickActionButtonSprite = GetNode<Sprite2D>("Button3");
		_button4Sprite = GetNode<Sprite2D>("Button4");

		_useButtonLabel = GetNode<Label>("Button1/Label");
		_removeButtonLabel = GetNode<Label>("Button2/Label");
		_quickActionButtonLabel = GetNode<Label>("Button3/Label");
		_button4Label = GetNode<Label>("Button4/Label");
	}

	private void ConnectSignals()
	{
		_useButton.Pressed += OnUseButtonPressed;
		_removeButton.Pressed += OnRemoveButtonPressed;
		_quickActionButton.Pressed += OnQuickActionButtonPressed;
		
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.ResourceUsed += OnResourceUsed;
		}
	}

	public void SetSelectedResource(ResourceType resourceType, int quantity)
	{
		_currentResourceType = resourceType;
		_currentQuantity = quantity;
		_isToolSelected = false;
		
		var itemInfo = ItemInformation.GetResourceInfo(resourceType);
		var texture = TextureManager.Instance?.GetResourceIconTexture(resourceType);
		
		UpdateDisplay(itemInfo, texture);
	}

	public void SetSelectedTool(ToolType toolType)
	{
		_currentToolType = toolType;
		_isToolSelected = true;
		_currentQuantity = 1;
		
		var itemInfo = ItemInformation.GetToolInfo(toolType);
		var texture = TextureManager.Instance?.GetToolTexture(toolType);
		
		UpdateDisplay(itemInfo, texture);
	}

	private void UpdateDisplay(ItemInfo itemInfo, Texture2D texture)
	{
		_selectedItem.Texture = texture;
		_resourceNameLabel.Text = itemInfo.Title;
		
		ShowDescriptionVariant(itemInfo.DescriptionType);
		_descriptionLabel.Text = itemInfo.Description;
		
		UpdateButtonStates(itemInfo);
	}

	private void ShowDescriptionVariant(DescriptionType descriptionType)
	{
		_descriptionVariant1.Visible = descriptionType == DescriptionType.TextOnly;
		_descriptionVariant2.Visible = false;
	}

	private void UpdateButtonStates(ItemInfo itemInfo)
	{
		bool canUse = itemInfo.CanBeUsed && !_isToolSelected;
		bool canAssignToQuick = itemInfo.CanAssignToQuickUse;
		
		SetButtonState(_useButton, _useButtonSprite, canUse);
		SetButtonState(_removeButton, _removeButtonSprite, true);
		SetButtonState(_quickActionButton, _quickActionButtonSprite, canAssignToQuick);
		SetButtonState(_button4, _button4Sprite, false);
	}

	private void SetButtonState(Button button, Sprite2D buttonSprite, bool enabled)
	{
		button.Disabled = !enabled;

		// Modulate button sprite alpha: 100% for enabled, 50% for disabled
		var spriteColor = buttonSprite.Modulate;
		spriteColor.A = enabled ? 1.0f : 0.5f;
		buttonSprite.Modulate = spriteColor;
	}

	private void OnUseButtonPressed() 
	{
		if (_isToolSelected) return;
		
		if (ItemInformation.CanBeUsed(_currentResourceType))
		{
			CommonSignals.Instance?.EmitUseResourceRequested(_currentResourceType);
		}
	}

	private void OnRemoveButtonPressed()
	{
		if (_isToolSelected) return;
		
		ResourcesManager.Instance?.RemoveResource(_currentResourceType, _currentQuantity);
		ClearSelection();
	}

	private void OnQuickActionButtonPressed()
	{
		bool canAssign = _isToolSelected ? 
			ItemInformation.CanAssignToQuickUse(_currentToolType) : 
			ItemInformation.CanAssignToQuickUse(_currentResourceType);
			
		if (!canAssign) return;
		
		var selectedToolPanel = GetNode<SelectedToolPanel>("/root/world/SelectedToolPanel");
		if (selectedToolPanel != null)
		{
			if (_isToolSelected)
			{
				selectedToolPanel.AssignToolToFirstEmptySlot(_currentToolType);
			}
			else
			{
				selectedToolPanel.AssignResourceToFirstEmptySlot(_currentResourceType);
			}
		}
	}

	private void OnResourceUsed(ResourceType resourceType, bool success)
	{
		if (!_isToolSelected && resourceType == _currentResourceType && success)
		{
			_currentQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(_currentResourceType);
			if (_currentQuantity <= 0)
			{
				ClearSelection();
			}
			else
			{
				// Update the display with the new quantity but keep selection active
				var itemInfo = ItemInformation.GetResourceInfo(_currentResourceType);
				UpdateButtonStates(itemInfo);
			}
		}
	}

	public void ClearSelection()
	{
		_selectedItem.Texture = null;
		_resourceNameLabel.Text = "";
		_descriptionLabel.Text = "";

		ShowDescriptionVariant(DescriptionType.TextOnly);

		SetButtonState(_useButton, _useButtonSprite, false);
		SetButtonState(_removeButton, _removeButtonSprite, false);
		SetButtonState(_quickActionButton, _quickActionButtonSprite, false);
		SetButtonState(_button4, _button4Sprite, false);

		_currentQuantity = 0;
		_isToolSelected = false;
	}
}
