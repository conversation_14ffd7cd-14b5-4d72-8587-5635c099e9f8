using Godot;

/// <summary>
/// Interface for objects that can be targeted by enemies
/// </summary>
public interface ICombatTarget
{
    /// <summary>
    /// Get the target type for priority calculation
    /// </summary>
    /// <returns>Target type enum value</returns>
    TargetType GetTargetType();
    
    /// <summary>
    /// Check if this target can currently be targeted
    /// </summary>
    /// <returns>True if target is valid</returns>
    bool CanBeTargeted();
    
    /// <summary>
    /// Get the position to target
    /// </summary>
    /// <returns>World position of target</returns>
    Vector2 GetTargetPosition();
    
    /// <summary>
    /// Called when an enemy starts targeting this object
    /// </summary>
    /// <param name="enemy">The enemy that is targeting this object</param>
    void OnTargeted(Node2D enemy);
    
    /// <summary>
    /// Called when this target is attacked by an enemy
    /// </summary>
    /// <param name="damage">Damage amount</param>
    /// <param name="attackerType">Type of enemy attacking</param>
    void OnAttacked(int damage, EnemyType attackerType);
}

/// <summary>
/// Interface for enemy AI and behavior
/// </summary>
public interface IEnemy
{
    /// <summary>
    /// Get the current state of the enemy
    /// </summary>
    /// <returns>Current enemy state</returns>
    EnemyState GetCurrentState();
    
    /// <summary>
    /// Set the enemy's target
    /// </summary>
    /// <param name="target">Target to pursue</param>
    void SetTarget(Node2D target);
    
    /// <summary>
    /// Set the enemy's territory center and radius
    /// </summary>
    /// <param name="center">Territory center position</param>
    /// <param name="radius">Territory radius</param>
    void SetTerritory(Vector2 center, float radius);
    
    /// <summary>
    /// Set whether the enemy should be aggressive
    /// </summary>
    /// <param name="aggressive">True for aggressive behavior</param>
    void SetAggressive(bool aggressive);
    
    /// <summary>
    /// Get the enemy type
    /// </summary>
    /// <returns>Enemy type enum</returns>
    EnemyType GetEnemyType();
    
    /// <summary>
    /// Get the assigned region ID
    /// </summary>
    /// <returns>Region ID</returns>
    int GetRegion();
    
    /// <summary>
    /// Set the assigned region ID
    /// </summary>
    /// <param name="regionId">Region ID to assign</param>
    void SetRegion(int regionId);
}
