using Godot;
using System.Collections.Generic;

public partial class MenuManager : Node
{
	public static MenuManager Instance { get; private set; }

	private Dictionary<string, IMenu> _registeredMenus = new Dictionary<string, IMenu>();
	private string _currentOpenMenu = null;

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
		}
		else
		{
			QueueFree();
		}
	}

	public void RegisterMenu(string menuId, IMenu menu)
	{
		if (!_registeredMenus.ContainsKey(menuId))
		{
			_registeredMenus[menuId] = menu;
			GD.Print($"MenuManager: Registered menu '{menuId}'");
		}
	}

	public void UnregisterMenu(string menuId)
	{
		if (_registeredMenus.ContainsKey(menuId))
		{
			if (_currentOpenMenu == menuId)
			{
				_currentOpenMenu = null;
			}
			_registeredMenus.Remove(menuId);
			GD.Print($"MenuManager: Unregistered menu '{menuId}'");
		}
	}

	public bool OpenMenu(string menuId)
	{
		if (!_registeredMenus.ContainsKey(menuId))
		{
			GD.PrintErr($"MenuManager: Menu '{menuId}' not registered!");
			return false;
		}

		// Close current menu if one is open
		if (_currentOpenMenu != null && _currentOpenMenu != menuId)
		{
			CloseCurrentMenu();
		}

		// Don't open if already open
		if (_currentOpenMenu == menuId)
		{
			return true;
		}

		var menu = _registeredMenus[menuId];
		menu.OpenMenu();
		_currentOpenMenu = menuId;
		GD.Print($"MenuManager: Opened menu '{menuId}'");
		return true;
	}

	public bool CloseMenu(string menuId)
	{
		if (!_registeredMenus.ContainsKey(menuId))
		{
			GD.PrintErr($"MenuManager: Menu '{menuId}' not registered!");
			return false;
		}

		if (_currentOpenMenu == menuId)
		{
			var menu = _registeredMenus[menuId];
			menu.CloseMenu();
			_currentOpenMenu = null;
			GD.Print($"MenuManager: Closed menu '{menuId}'");
			return true;
		}

		return false;
	}

	public void CloseCurrentMenu()
	{
		if (_currentOpenMenu != null)
		{
			CloseMenu(_currentOpenMenu);
		}
	}

	public void CloseAllMenus()
	{
		if (_currentOpenMenu != null)
		{
			CloseCurrentMenu();
		}
	}

	public bool IsMenuOpen(string menuId)
	{
		return _currentOpenMenu == menuId;
	}

	public bool IsAnyMenuOpen()
	{
		return _currentOpenMenu != null;
	}

	public string GetCurrentOpenMenu()
	{
		return _currentOpenMenu;
	}

	public IMenu GetRegisteredMenu(string menuId)
	{
		return _registeredMenus.TryGetValue(menuId, out var menu) ? menu : null;
	}

	public bool ToggleMenu(string menuId)
	{
		if (_currentOpenMenu == menuId)
		{
			CloseMenu(menuId);
			return false; // Menu was closed
		}
		else
		{
			OpenMenu(menuId);
			return true; // Menu was opened
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (@event is InputEventKey keyEvent && keyEvent.Pressed && keyEvent.Keycode == Key.Escape)
		{
			if (IsAnyMenuOpen())
			{
				CloseAllMenus();
				GetViewport().SetInputAsHandled();
			}
		}
	}
}

public interface IMenu
{
	void OpenMenu();
	void CloseMenu();
	bool IsMenuOpen();
}
