using Godot;
using System.Collections.Generic;

public partial class TutorialNPC : Sprite2D
{
	private enum QuestState
	{
		Welcome,
		Welcome2,
		BuildAnvil,
		WaitForBridge,
		BuildBridge,
		WaitF<PERSON><PERSON><PERSON><PERSON>,
		HuntR<PERSON><PERSON>,
		<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		B<PERSON><PERSON>amp<PERSON>,
		WaitForCooking,
		Complete
	}

	private QuestState _currentQuest = QuestState.Welcome;
	private bool _isPlayerInRange = false;
	private bool _isDialogOpen = false;
	private int _currentDialogIndex = 0;
	private bool _isTypewriterActive = false;
	private bool _hasSpawnedCompletionReward = false;
	private Tween _typewriterTween;

	// Dialog components
	private Node2D _dialogNode;
	private Sprite2D _dialogPanel;
	private Sprite2D _continueMark;
	private Label _dialogLabel;
	private Button _dialogContinueButton;
	private Area2D _playerDetectionArea;
	private AnimationPlayer _disapearAnimationPlayer;

	// Dialog translation keys for each quest state
	private readonly Dictionary<QuestState, List<string>> _questDialogKeys = new()
	{
		{
			QuestState.Welcome,
			new List<string>
			{
				"NPC_TUTORIAL_WELCOME_1",
				"NPC_TUTORIAL_WELCOME_2",
				"NPC_TUTORIAL_WELCOME_3",
				"NPC_TUTORIAL_WELCOME_4"
			}
		},
		{
			QuestState.Welcome2,
			new List<string>
			{
				"NPC_TUTORIAL_WELCOME2_1"
			}
		},
		{
			QuestState.BuildAnvil,
			new List<string>
			{
				"NPC_TUTORIAL_BUILDANVIL_1",
				"NPC_TUTORIAL_BUILDANVIL_2",
				"NPC_TUTORIAL_BUILDANVIL_3",
				"NPC_TUTORIAL_BUILDANVIL_4"
			}
		},
		{
			QuestState.WaitForBridge,
			new List<string>
			{
				"NPC_TUTORIAL_WAITFORBRIDGE_1"
			}
		},
		{
			QuestState.BuildBridge,
			new List<string>
			{
				"NPC_TUTORIAL_BUILDBRIDGE_1",
				"NPC_TUTORIAL_BUILDBRIDGE_2",
				"NPC_TUTORIAL_BUILDBRIDGE_3",
				"NPC_TUTORIAL_BUILDBRIDGE_4"
			}
		},
		{
			QuestState.WaitForRabbit,
			new List<string>
			{
				"NPC_TUTORIAL_WAITFORRABBIT_1"
			}
		},
		{
			QuestState.HuntRabbit,
			new List<string>
			{
				"NPC_TUTORIAL_HUNTRABBIT_1",
				"NPC_TUTORIAL_HUNTRABBIT_2",
				"NPC_TUTORIAL_HUNTRABBIT_3",
				"NPC_TUTORIAL_HUNTRABBIT_4"
			}
		},
		{
			QuestState.WaitForCampfire,
			new List<string>
			{
				"NPC_TUTORIAL_WAITFORCAMPFIRE_1"
			}
		},
		{
			QuestState.BuildCampfire,
			new List<string>
			{
				"NPC_TUTORIAL_BUILDCAMPFIRE_1",
				"NPC_TUTORIAL_BUILDCAMPFIRE_2",
				"NPC_TUTORIAL_BUILDCAMPFIRE_3",
				"NPC_TUTORIAL_BUILDCAMPFIRE_4"
			}
		},
		{
			QuestState.WaitForCooking,
			new List<string>
			{
				"NPC_TUTORIAL_WAITFORCOOKING_1"
			}
		},
		{
			QuestState.Complete,
			new List<string>
			{
				"NPC_TUTORIAL_COMPLETE_1",
				"NPC_TUTORIAL_COMPLETE_2",
				"NPC_TUTORIAL_COMPLETE_3"
			}
		}
	};

	public override void _Ready()
	{
		_dialogNode = GetNode<Node2D>("Dialog");
		_dialogPanel = GetNode<Sprite2D>("Dialog/DialogPanel");
		_continueMark = GetNode<Sprite2D>("Dialog/ContinueMark");
		_dialogLabel = GetNode<Label>("Dialog/Label");
		_dialogContinueButton = GetNode<Button>("Dialog/DialogContinueButton");
		_playerDetectionArea = GetNode<Area2D>("PlayerDetectionArea2D");
		_disapearAnimationPlayer = GetNode<AnimationPlayer>("DisapearAnimation/AnimationPlayer");
		_disapearAnimationPlayer.AnimationFinished += OnDisapearAnimationFinished;
		GetNode<Sprite2D>("DisapearAnimation/Sprite2D").Visible = false;
		
		if (_playerDetectionArea != null)
		{
			// Set collision mask to detect PlayerDetector (layer 3)
			_playerDetectionArea.CollisionMask = 4; // Detect layer 3 (bit 2 = 4)
			_playerDetectionArea.AreaEntered += OnPlayerEntered;
			_playerDetectionArea.AreaExited += OnPlayerExited;
		}

		if (_dialogContinueButton != null)
		{
			_dialogContinueButton.Pressed += OnDialogContinuePressed;
		}

		// Connect to building and hunting signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.AnvilBuilt += OnAnvilBuilt;
			CommonSignals.Instance.BridgeBuilt += OnBridgeBuilt;
			CommonSignals.Instance.RabbitHunted += OnRabbitHunted;
			CommonSignals.Instance.CampfireBuilt += OnCampfireBuilt;
			CommonSignals.Instance.RabbitLegCooked += OnRabbitLegCooked;
		}

		LoadQuestProgress();
		HideDialog();
	}

	public override void _ExitTree()
	{
		if (_playerDetectionArea != null)
		{
			_playerDetectionArea.AreaEntered -= OnPlayerEntered;
			_playerDetectionArea.AreaExited -= OnPlayerExited;
		}

		if (_dialogContinueButton != null)
		{
			_dialogContinueButton.Pressed -= OnDialogContinuePressed;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.AnvilBuilt -= OnAnvilBuilt;
			CommonSignals.Instance.BridgeBuilt -= OnBridgeBuilt;
			CommonSignals.Instance.RabbitHunted -= OnRabbitHunted;
			CommonSignals.Instance.CampfireBuilt -= OnCampfireBuilt;
			CommonSignals.Instance.RabbitLegCooked -= OnRabbitLegCooked;
		}

		_disapearAnimationPlayer.AnimationFinished -= OnDisapearAnimationFinished;
	}

	private void OnDisapearAnimationFinished(StringName animation)
	{
		SpawnCompletionReward();
		QueueFree();
	}

	public override void _Input(InputEvent @event)
	{
		if (!_isPlayerInRange || _isDialogOpen) return;

		if (@event is InputEventKey keyEvent && keyEvent.Pressed)
		{
			if (keyEvent.Keycode == Key.R)
			{
				StartDialog();
			}
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		_isPlayerInRange = true;
		GD.Print("TutorialNPC: Player in range - press 'R' to talk");
	}

	private void OnPlayerExited(Area2D area)
	{
		_isPlayerInRange = false;
		if (_isDialogOpen)
		{
			HideDialog();
		}
	}

	private void StartDialog()
	{
		if (!_questDialogKeys.TryGetValue(_currentQuest, out var dialogKeys) || dialogKeys.Count == 0)
			return;

		_isDialogOpen = true;
		_currentDialogIndex = 0;
		ShowDialog();
		UpdateDialogText();

		// Disable player movement during dialog
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);
	}

	private void OnDialogContinuePressed()
	{
		if (_isTypewriterActive)
		{
			CompleteTypewriterEffect();
			return;
		}

		if (!_questDialogKeys.TryGetValue(_currentQuest, out var dialogKeys))
			return;

		_currentDialogIndex++;

		if (_currentDialogIndex >= dialogKeys.Count)
		{
			HideDialog();

			if (_currentQuest == QuestState.Welcome)
			{
				_currentQuest = QuestState.Welcome2;
				SaveQuestProgress();
			}
			else if (_currentQuest == QuestState.BuildAnvil)
			{
				_currentQuest = QuestState.WaitForBridge;
				SaveQuestProgress();
			}
			else if (_currentQuest == QuestState.BuildBridge)
			{
				_currentQuest = QuestState.WaitForRabbit;
				SaveQuestProgress();
			}
			else if (_currentQuest == QuestState.HuntRabbit)
			{
				_currentQuest = QuestState.WaitForCampfire;
				SaveQuestProgress();
			}
			else if (_currentQuest == QuestState.BuildCampfire)
			{
				_currentQuest = QuestState.WaitForCooking;
				SaveQuestProgress();
			}
			else if (_currentQuest == QuestState.Complete)
			{
				CommonSignals.Instance?.EmitTutorialCompleted();
				_disapearAnimationPlayer.Play("HideNpc");
			}
		}
		else
		{
			UpdateDialogText();
		}
	}

	private void ShowDialog()
	{
		if (_dialogNode != null)
		{
			_dialogNode.Visible = true;
		}
	}

	private void HideDialog()
	{
		if (_dialogNode != null)
		{
			_dialogNode.Visible = false;
		}

		_isDialogOpen = false;
		_currentDialogIndex = 0;

		// Re-enable player movement
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);
	}

	private void UpdateDialogText()
	{
		if (!_questDialogKeys.TryGetValue(_currentQuest, out var dialogKeys) ||
			_currentDialogIndex >= dialogKeys.Count || _dialogLabel == null)
			return;

		string translationKey = dialogKeys[_currentDialogIndex];
		string fullText = Tr(translationKey);
		StartTypewriterEffect(fullText);
	}

	private void StartTypewriterEffect(string fullText)
	{
		if (_dialogLabel == null) return;

		_isTypewriterActive = true;
		_dialogLabel.Text = "";

		_typewriterTween?.Kill();
		_typewriterTween = CreateTween();

		float totalDuration = fullText.Length * 0.05f;

		for (int i = 0; i <= fullText.Length; i++)
		{
			string partialText = fullText.Substring(0, i);
			float delay = 0.05f;// DO NOT CHANGE THIS! it has to be 0.05f

			_typewriterTween.TweenCallback(Callable.From(() => {
				if (_dialogLabel != null)
					_dialogLabel.Text = partialText;
			})).SetDelay(delay);
		}

		_typewriterTween.TweenCallback(Callable.From(() => {
			_isTypewriterActive = false;
		})).SetDelay(totalDuration);
	}

	private void CompleteTypewriterEffect()
	{
		if (!_questDialogKeys.TryGetValue(_currentQuest, out var dialogKeys) ||
			_currentDialogIndex >= dialogKeys.Count || _dialogLabel == null)
			return;

		_typewriterTween?.Kill();
		string translationKey = dialogKeys[_currentDialogIndex];
		_dialogLabel.Text = Tr(translationKey);
		_isTypewriterActive = false;
	}

	private void OnAnvilBuilt()
	{
		if (_currentQuest == QuestState.Welcome2)
		{
			_currentQuest = QuestState.BuildAnvil;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Anvil built! Player can now talk to NPC.");
		}
	}

	private void OnBridgeBuilt()
	{
		if (_currentQuest == QuestState.WaitForBridge)
		{
			_currentQuest = QuestState.BuildBridge;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Bridge built! Player can now talk to NPC.");
		}
	}

	private void OnRabbitHunted()
	{
		if (_currentQuest == QuestState.WaitForRabbit)
		{
			_currentQuest = QuestState.HuntRabbit;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Rabbit hunted! Player can now talk to NPC.");
		}
	}

	private void OnCampfireBuilt()
	{
		if (_currentQuest == QuestState.WaitForCampfire)
		{
			_currentQuest = QuestState.BuildCampfire;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Campfire built! Player can now talk to NPC.");
		}
	}

	private void OnRabbitLegCooked()
	{
		if (_currentQuest == QuestState.WaitForCooking)
		{
			_currentQuest = QuestState.Complete;
			SaveQuestProgress();
			GD.Print("TutorialNPC: Rabbit leg cooked! Player can now talk to NPC for completion.");
		}
	}

	public void OnMeatCooked()
	{
		OnRabbitLegCooked();
	}

	private void SpawnCompletionReward()
	{
		if (_hasSpawnedCompletionReward || GameSaveData.Instance.TutorialCompletionRewardSpawned)
		{
			GD.Print("TutorialNPC: Completion reward already spawned, skipping");
			return;
		}

		var chest = Chest.SpawnChest(GlobalPosition, Chest.ChestType.WoodenChest, 50, []);
		if (chest != null)
		{
			_hasSpawnedCompletionReward = true;
			GameSaveData.Instance.TutorialCompletionRewardSpawned = true;
			// Force save to ensure chest persists through game restart
			var resourcesManager = ResourcesManager.Instance;
			resourcesManager?.ForceSave();
			GD.Print("TutorialNPC: Spawned completion reward chest and saved game!");
		}
	}

	private void HideNPC()
	{
		QueueFree();
	}

	private void SaveQuestProgress()
	{
		// Save quest progress in game data
		GameSaveData.Instance.TutorialQuestState = (int)_currentQuest;
		ResourcesManager.Instance?.ForceSave();
	}

	private void LoadQuestProgress()
	{
		_currentQuest = (QuestState)GameSaveData.Instance.TutorialQuestState;
		_hasSpawnedCompletionReward = GameSaveData.Instance.TutorialCompletionRewardSpawned;

		if (_currentQuest == QuestState.Complete)
		{
			HideNPC();
		}
	}
}
