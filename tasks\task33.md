Implement following tasks. Make sure you deeply understand how it all works so that you correctly fix issues:

TASK-1:
I get error:
  ERROR: ResourcesManager: Failed to deserialize rabbit data for region 1: The JSON value could not be converted to Rabbit+Direction. Path: $[0].currentDirection | LineNumber: 0 | BytePositionInLine: 130.

TASK-2:
Look how Tree is positioned (in both tscn and cs) - i want Tree2 to be positioned the same way (y position, offset etc). Currently tree2 is too high.

TASK-3:
In shop menu, we have buttons ButtonSetOne and ButtonSet25Percent - see ShopMenu.tscn. They don't work. Look at ButtonSet50Percent or ButtonSetMax - they work so the logic should be similar but set 1 or 25%.

TASK-4:
I have EffectPlayer.tscn - look how it's used by looking at Rock.cs and Rock.tscn. Also read EffectPlayer.tscn and EffectPlayer.cs. Now, in ShopMenu - add this effect player to tscn, and in ShopMenu.cs and add exports for 2 sounds - ClickAudio and PurchaseAudio - when player clicks ButtonSetOne, ButtonSet25Per<PERSON>, ButtonSet50Percent, ButtonSetMax, ButtonPlusOne, ButtonMinusOne or CloseButton then play ClickAudio. When player buys something (clicks ItemButton) or ButtonSell then play PurchaseAudio.