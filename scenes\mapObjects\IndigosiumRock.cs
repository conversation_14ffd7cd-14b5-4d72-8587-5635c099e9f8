using Godot;
using System;
using System.Threading.Tasks;

public partial class IndigosiumRock : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 12;
	[Export] public ResourceType ResourceType { get; set; } = ResourceType.IndigosiumOre;
	[Export] public int ResourceAmount { get; set; } = 2;
	[Export] public AudioStream RockDestroyedAudio { get; set; }
	[Export] public AudioStream RockHit { get; set; }
	[Signal] public delegate void IndigosiumRockDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;
	private EffectPlayer _effectPlayer;

	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f;

	public override void _Ready()
	{
		_effectPlayer = GetNode<EffectPlayer>("EffectPlayer");
		_currentHealth = MaxHealth;
		AddToGroup("rocks");
		_sprite = GetNode<Sprite2D>("Sprite2D");
		if (_sprite == null)
		{
			GD.PrintErr("IndigosiumRock: Sprite2D node not found!");
			return;
		}
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("IndigosiumRock: ProgressBar node not found!");
		}
		YSortEnabled = true;
		_sprite.Position = Vector2.Zero;
		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("IndigosiumRock: CustomDataLayerManager not found!");
		}
		if (_tilePosition == Vector2I.Zero)
		{
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Rock);
		}
		UpdateHPBar();
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;
		_currentHealth -= damage;
		UpdateHPBar();
		PlayHitAnimation();
		if (_currentHealth <= 0)
		{
			_effectPlayer.Play(RockDestroyedAudio);
			DestroyRockAsync();
		}
		else
		{
			_effectPlayer.Play(RockHit);
		}
	}

	private void PlayHitAnimation()
	{
		if (_sprite == null) return;
		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);
		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);
		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;
		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	private async Task DestroyRockAsync()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;
		DropResources();
		_customDataManager?.ClearObjectPlaced(_tilePosition);
		CommonSignals.Instance?.EmitAddXp(3);
		EmitSignal(SignalName.IndigosiumRockDestroyed, _tilePosition);
        await ToSignal(GetTree().CreateTimer(0.4f), "timeout");
		QueueFree();
	}

	private void DropResources()
	{
		for (int i = 0; i < ResourceAmount; i++)
		{
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 8.0f,
				(float)(GD.Randf() - 0.5f) * 8.0f
			);
			Vector2 spawnPosition = GlobalPosition + offset;
			DroppedResource.SpawnResource(spawnPosition, ResourceType, 1);
		}
	}

	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8);
	}

	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	private void UpdateHPBar()
	{
		if (_hpBar == null) return;
		float healthPercentage = (float)_currentHealth / MaxHealth;
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
