using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// Territory area for enemies using Area2D approach
/// Provides better control over enemy territorial behavior
/// </summary>
public partial class EnemyTerritory : Area2D
{
    [Export] public float TerritoryRadius { get; set; } = 120.0f;
    [Export] public bool ShowDebugVisual { get; set; } = false;
    [Export] public Color TerritoryColor { get; set; } = new Color(1.0f, 0.0f, 0.0f, 0.2f);

    private List<BaseEnemy> _assignedEnemies = new();
    private CollisionShape2D _territoryShape;
    private Node2D _debugVisual;
    private bool _playerInTerritory = false;

    public override void _Ready()
    {
        SetupTerritoryArea();
        SetupDebugVisual();
        
        // Connect area signals
        BodyEntered += OnBodyEntered;
        BodyExited += OnBodyExited;
        
        // Set collision layers for territory detection
        CollisionLayer = 0; // Territory areas don't collide with anything
        CollisionMask = 1;  // Detect player and other bodies on layer 1
    }

    private void SetupTerritoryArea()
    {
        _territoryShape = new CollisionShape2D();
        var circleShape = new CircleShape2D();
        circleShape.Radius = TerritoryRadius;
        _territoryShape.Shape = circleShape;
        AddChild(_territoryShape);
    }

    private void SetupDebugVisual()
    {
        if (!ShowDebugVisual) return;

        _debugVisual = new Node2D();
        _debugVisual.Name = "DebugVisual";
        AddChild(_debugVisual);
    }

    public override void _Draw()
    {
        if (ShowDebugVisual)
        {
            DrawCircle(Vector2.Zero, TerritoryRadius, TerritoryColor);
            
            // Draw border
            DrawArc(Vector2.Zero, TerritoryRadius, 0, Mathf.Pi * 2, 64, 
                   new Color(TerritoryColor.R, TerritoryColor.G, TerritoryColor.B, 0.8f), 2.0f);
        }
    }

    /// <summary>
    /// Assign an enemy to this territory
    /// </summary>
    public void AssignEnemy(BaseEnemy enemy)
    {
        if (enemy == null || _assignedEnemies.Contains(enemy)) return;

        _assignedEnemies.Add(enemy);
        enemy.SetTerritory(GlobalPosition, TerritoryRadius);
        
        // Connect to enemy's territory area if it's a MeleeGoblin
        if (enemy is MeleeGoblin goblin)
        {
            goblin.SetTerritoryArea(this);
        }

        GD.Print($"EnemyTerritory: Assigned {enemy.GetEnemyType()} to territory at {GlobalPosition}");
    }

    /// <summary>
    /// Remove an enemy from this territory
    /// </summary>
    public void RemoveEnemy(BaseEnemy enemy)
    {
        if (enemy == null) return;

        _assignedEnemies.Remove(enemy);
        GD.Print($"EnemyTerritory: Removed {enemy.GetEnemyType()} from territory");
    }

    /// <summary>
    /// Get all enemies assigned to this territory
    /// </summary>
    public List<BaseEnemy> GetAssignedEnemies()
    {
        // Clean up dead enemies
        _assignedEnemies.RemoveAll(enemy => !IsInstanceValid(enemy));
        return new List<BaseEnemy>(_assignedEnemies);
    }

    /// <summary>
    /// Check if player is currently in territory
    /// </summary>
    public bool IsPlayerInTerritory()
    {
        return _playerInTerritory;
    }

    /// <summary>
    /// Get the center position of this territory
    /// </summary>
    public Vector2 GetTerritoryCenter()
    {
        return GlobalPosition;
    }

    /// <summary>
    /// Check if a position is within this territory
    /// </summary>
    public bool IsPositionInTerritory(Vector2 position)
    {
        return GlobalPosition.DistanceTo(position) <= TerritoryRadius;
    }

    /// <summary>
    /// Get a random patrol point within the territory
    /// </summary>
    public Vector2 GetRandomPatrolPoint()
    {
        var random = new Random();
        float angle = (float)(random.NextDouble() * Math.PI * 2);
        float distance = (float)(random.NextDouble() * TerritoryRadius * 0.8f);
        
        return GlobalPosition + new Vector2(
            Mathf.Cos(angle) * distance,
            Mathf.Sin(angle) * distance
        );
    }

    /// <summary>
    /// Alert all enemies in territory about a threat
    /// </summary>
    public void AlertEnemies(Node2D threat)
    {
        foreach (var enemy in GetAssignedEnemies())
        {
            if (enemy.GetCurrentState() == EnemyState.Patrolling)
            {
                enemy.SetTarget(threat);
                GD.Print($"EnemyTerritory: Alerted {enemy.GetEnemyType()} about threat");
            }
        }
    }

    /// <summary>
    /// Make all enemies in territory return to patrolling
    /// </summary>
    public void CalmEnemies()
    {
        foreach (var enemy in GetAssignedEnemies())
        {
            if (enemy.GetCurrentState() == EnemyState.Pursuing && !enemy.GetSaveData().WasHitByPlayer)
            {
                enemy.SetTarget(null);
                GD.Print($"EnemyTerritory: Calmed {enemy.GetEnemyType()}");
            }
        }
    }

    private void OnBodyEntered(Node2D body)
    {
        if (body is PlayerController player)
        {
            _playerInTerritory = true;
            GD.Print($"EnemyTerritory: Player entered territory at {GlobalPosition}");
            
            // Alert all territorial enemies
            AlertEnemies(player);
        }
    }

    private void OnBodyExited(Node2D body)
    {
        if (body is PlayerController)
        {
            _playerInTerritory = false;
            GD.Print($"EnemyTerritory: Player left territory at {GlobalPosition}");
            
            // Calm down enemies that weren't hit by player
            CalmEnemies();
        }
    }

    /// <summary>
    /// Update territory radius and collision shape
    /// </summary>
    public void SetTerritoryRadius(float radius)
    {
        TerritoryRadius = radius;
        
        if (_territoryShape?.Shape is CircleShape2D circleShape)
        {
            circleShape.Radius = radius;
        }
        
        // Update all assigned enemies
        foreach (var enemy in _assignedEnemies)
        {
            enemy.SetTerritory(GlobalPosition, radius);
        }
        
        QueueRedraw(); // Redraw debug visual
    }

    /// <summary>
    /// Enable or disable debug visualization
    /// </summary>
    public void SetDebugVisual(bool enabled)
    {
        ShowDebugVisual = enabled;
        QueueRedraw();
    }

    /// <summary>
    /// Get territory data for saving
    /// </summary>
    public TerritoryData GetTerritoryData()
    {
        return new TerritoryData
        {
            Position = GlobalPosition,
            Radius = TerritoryRadius,
            AssignedEnemyCount = _assignedEnemies.Count
        };
    }

    /// <summary>
    /// Load territory from save data
    /// </summary>
    public void LoadFromTerritoryData(TerritoryData data)
    {
        GlobalPosition = data.Position;
        SetTerritoryRadius(data.Radius);
    }
}

/// <summary>
/// Save data for enemy territories
/// </summary>
[System.Serializable]
public class TerritoryData
{
    public Vector2 Position { get; set; }
    public float Radius { get; set; }
    public int AssignedEnemyCount { get; set; }
}
