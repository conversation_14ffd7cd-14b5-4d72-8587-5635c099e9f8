using Godot;

public partial class Furnace1Menu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _copperBarSelectButton;
	private Button _ironBarSelectButton;
	private Button _copperKeySelectButton;
	private Button _ironKeySelectButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private Furnace1 _currentFurnace;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_copperBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCopperBar/Button");
		_ironBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListIronBar/Button");
		_copperKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCopperKey/Button");
		_ironKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListIronKey/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_copperBarSelectButton.Pressed += OnCopperBarSelectButtonPressed;
		_ironBarSelectButton.Pressed += OnIronBarSelectButtonPressed;
		_copperKeySelectButton.Pressed += OnCopperKeySelectButtonPressed;
		_ironKeySelectButton.Pressed += OnIronKeySelectButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("Furnace1Menu: Ready completed");

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("Furnace1Menu", this);
	}

	public void SetFurnace(Furnace1 furnace)
	{
		_currentFurnace = furnace;
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnCopperBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.CopperBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnIronBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.IronBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnCopperKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.CopperKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnIronKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.IronKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 0)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		if (_selectedAmount < 999)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_selectedResource == ResourceType.None || _selectedAmount <= 0 || _currentFurnace == null)
			return;

		// Verify player has enough resources before starting production
		int maxAffordable = GetMaxAffordableAmount();
		if (maxAffordable < _selectedAmount)
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		_currentFurnace.StartSmelting(_selectedResource, _selectedAmount);
		CloseMenu();
	}

	private void UpdateInfoBoard()
	{
		if (_selectedResource == ResourceType.None)
		{
			_itemFront.Texture = null;
			_amountToProduce.Text = "0";
			return;
		}

		var textureManager = TextureManager.Instance;
		if (textureManager != null)
		{
			_itemFront.Texture = textureManager.GetResourceTexture(_selectedResource);
		}

		_amountToProduce.Text = _selectedAmount.ToString();
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None)
			return 0;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
			return 0;

		int maxAmount = 999;
		switch (_selectedResource)
		{
			case ResourceType.CopperBar:
				int copperOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.CopperOre);
				int wood = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(copperOre / 3, wood / 1);
				break;
			case ResourceType.IronBar:
				int ironOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.IronOre);
				int wood2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(ironOre / 3, wood2 / 1);
				break;
			case ResourceType.CopperKey:
				int copperBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.CopperBar);
				int charcoal = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(copperBars / 4, charcoal / 1);
				break;
			case ResourceType.IronKey:
				int ironBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.IronBar);
				int charcoal2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(ironBars / 4, charcoal2 / 1);
				break;
		}

		return Mathf.Max(0, maxAmount);
	}

	public void OpenMenu(Furnace1 furnace)
	{
		_currentFurnace = furnace;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
		GD.Print("Furnace1Menu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentFurnace = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			GetNode<Node2D>("Control/Panel").Visible = false;
		}

		GD.Print("Furnace1Menu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires furnace to be set externally
		if (_currentFurnace != null)
		{
			OpenMenu(_currentFurnace);
		}
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Node2D>("Control/Panel");
		return panel != null && panel.Visible;
	}
}
