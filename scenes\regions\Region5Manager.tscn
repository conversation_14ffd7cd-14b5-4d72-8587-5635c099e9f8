[gd_scene load_steps=8 format=3 uid="uid://cng2rd0vxdgki"]

[ext_resource type="Script" path="res://scenes/regions/Region5Manager.cs" id="1_region5_script"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/enemies/EnemyTerritory.tscn" id="2_territory"]
[ext_resource type="PackedScene" uid="uid://c8xvoaywqxhqy" path="res://scenes/enemies/MeleeGoblin.tscn" id="3_goblin"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/objects/Tree.tscn" id="4_tree"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/objects/Rock.tscn" id="5_rock"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/objects/BerryBush.tscn" id="6_berry"]
[ext_resource type="PackedScene" uid="uid://bqxm8y4n5k2vw" path="res://scenes/objects/GreenBush.tscn" id="7_green"]

[node name="Region5Manager" type="Node2D"]
script = ExtResource("1_region5_script")
BaseSpawnInterval = 15.0
RegionId = 5
InitiallySpawnedObjects = 15
MaxItemsSpawned = 50
TreeSpawnWeight = 30
RockSpawnWeight = 25
BerryBushSpawnWeight = 20
GreenBushSpawnWeight = 15
Rock2SpawnWeight = 10
TreeScene = ExtResource("4_tree")
RockScene = ExtResource("5_rock")
BerryBushScene = ExtResource("6_berry")
GreenBushScene = ExtResource("7_green")
MaxEnemiesPerRegion = 2
EnemySpawnInterval = 120.0
GoblinScene = ExtResource("3_goblin")
