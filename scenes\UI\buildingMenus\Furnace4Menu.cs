using Godot;

public partial class Furnace4Menu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _adamantiteBarSelectButton;
	private Button _uraniumBarSelectButton;
	private Button _adamantiteKeySelectButton;
	private Button _uraniumKeySelectButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private Furnace4 _currentFurnace;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_adamantiteBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAdamantiteBar/Button");
		_uraniumBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListUraniumBar/Button");
		_adamantiteKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListAdamantiteKey/Button");
		_uraniumKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListUraniumKey/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_adamantiteBarSelectButton.Pressed += OnAdamantiteBarSelectButtonPressed;
		_uraniumBarSelectButton.Pressed += OnUraniumBarSelectButtonPressed;
		_adamantiteKeySelectButton.Pressed += OnAdamantiteKeySelectButtonPressed;
		_uraniumKeySelectButton.Pressed += OnUraniumKeySelectButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("Furnace4Menu: Ready completed");

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("Furnace4Menu", this);
	}

	public void SetFurnace(Furnace4 furnace)
	{
		_currentFurnace = furnace;
	}

	public void OpenMenu(Furnace4 furnace)
	{
		_currentFurnace = furnace;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
		GD.Print("Furnace4Menu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentFurnace = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			GetNode<Node2D>("Control/Panel").Visible = false;
		}

		GD.Print("Furnace4Menu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires furnace to be set externally
		if (_currentFurnace != null)
		{
			OpenMenu(_currentFurnace);
		}
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Node2D>("Control/Panel");
		return panel != null && panel.Visible;
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnAdamantiteBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.AdamantiteBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnUraniumBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.UraniumBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnAdamantiteKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.AdamantiteKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnUraniumKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.UraniumKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 0)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		if (_selectedAmount < 999)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_currentFurnace == null || _selectedResource == ResourceType.None || _selectedAmount <= 0)
			return;

		if (!_currentFurnace.CanAffordRecipe(_selectedResource, _selectedAmount))
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		if (_currentFurnace.ConsumeRecipeResources(_selectedResource, _selectedAmount))
		{
			_currentFurnace.StartSmelting(_selectedResource, _selectedAmount);
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_selectedResource == ResourceType.None)
		{
			if (_itemFront != null)
			{
				_itemFront.Texture = null;
			}
			if (_amountToProduce != null)
			{
				_amountToProduce.Text = "0";
			}
			return;
		}

		// Update item sprite
		if (_itemFront != null)
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				_itemFront.Texture = textureManager.GetResourceTexture(_selectedResource);
			}
		}

		// Update amount label
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _selectedAmount.ToString();
		}
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None)
			return 0;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
			return 0;

		int maxAmount = 999;
		switch (_selectedResource)
		{
			case ResourceType.AdamantiteBar:
				int adamantiteOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.AdamantiteOre);
				int wood = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(adamantiteOre / 3, wood / 1);
				break;
			case ResourceType.UraniumBar:
				int uraniumOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.UraniumOre);
				int wood2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(uraniumOre / 3, wood2 / 1);
				break;
			case ResourceType.AdamantiteKey:
				int adamantiteBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.AdamantiteBar);
				int charcoal = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(adamantiteBars / 4, charcoal / 1);
				break;
			case ResourceType.UraniumKey:
				int uraniumBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.UraniumBar);
				int charcoal2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(uraniumBars / 4, charcoal2 / 1);
				break;
		}

		return Mathf.Max(0, maxAmount);
	}
}
