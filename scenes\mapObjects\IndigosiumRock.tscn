[gd_scene load_steps=7 format=3 uid="uid://bpc6s0bqfd60n"]

[ext_resource type="Script" uid="uid://6jitl1eebyh6" path="res://scenes/mapObjects/IndigosiumRock.cs" id="1_indigosium_rock"]
[ext_resource type="AudioStream" uid="uid://c7wmjx041h8iv" path="res://resources/audio/ovani/Rock Dirt Impact Dull D.ogg" id="2_rn0qs"]
[ext_resource type="Texture2D" uid="uid://bknp75wggxjmj" path="res://resources/solaria/exterior/indigosiumStone.png" id="2_sdyhw"]
[ext_resource type="AudioStream" uid="uid://46yex8wh4k8c" path="res://resources/audio/ovani/Chisel C.ogg" id="3_46ver"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="6_0iylc"]

[node name="IndigosiumRock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_indigosium_rock")
RockDestroyedAudio = ExtResource("2_rn0qs")
RockHit = ExtResource("3_46ver")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_sdyhw")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 5, 5, -5, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)

[node name="EffectPlayer" parent="." instance=ExtResource("6_0iylc")]
