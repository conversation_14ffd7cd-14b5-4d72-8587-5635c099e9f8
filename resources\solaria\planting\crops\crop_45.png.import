[remap]

importer="texture"
type="CompressedTexture2D"
uid="uid://j4vu0gxvc3ro"
path="res://.godot/imported/crop_45.png-9a17af9e9c79771f7c9e667986fd717e.ctex"
metadata={
"vram_texture": false
}

[deps]

source_file="res://resources/solaria/planting/crops/crop_45.png"
dest_files=["res://.godot/imported/crop_45.png-9a17af9e9c79771f7c9e667986fd717e.ctex"]

[params]

compress/mode=0
compress/high_quality=false
compress/lossy_quality=0.7
compress/hdr_compression=1
compress/normal_map=0
compress/channel_pack=0
mipmaps/generate=false
mipmaps/limit=-1
roughness/mode=0
roughness/src_normal=""
process/fix_alpha_border=true
process/premult_alpha=false
process/normal_map_invert_y=false
process/hdr_as_srgb=false
process/hdr_clamp_exposure=false
process/size_limit=0
detect_3d/compress_to=1
