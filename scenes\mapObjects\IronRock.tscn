[gd_scene load_steps=7 format=3 uid="uid://cptt5vscnu7gu"]

[ext_resource type="Script" uid="uid://dxoqrw4hjsek6" path="res://scenes/mapObjects/IronRock.cs" id="1_iron_rock"]
[ext_resource type="AudioStream" uid="uid://c7wmjx041h8iv" path="res://resources/audio/ovani/Rock Dirt Impact Dull D.ogg" id="2_cg7dw"]
[ext_resource type="Texture2D" uid="uid://dh5fwtghkq8gd" path="res://resources/solaria/exterior/ironStone.png" id="2_dfqms"]
[ext_resource type="AudioStream" uid="uid://46yex8wh4k8c" path="res://resources/audio/ovani/Chisel C.ogg" id="3_5rl34"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="6_d30cg"]

[node name="IronRock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_iron_rock")
RockDestroyedAudio = ExtResource("2_cg7dw")
RockHit = ExtResource("3_5rl34")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_dfqms")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 5, 5, -5, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)

[node name="EffectPlayer" parent="." instance=ExtResource("6_d30cg")]
