We want to create a farming system where plater will be able to plant.
TASKS:
1. Add new ResourceTypes, resource images + icon in texture manager and data in ItemInformation and translations (translate titles - which is plant name and description for all should be "Consumable" - but this also translate) for plants AND the same for SeedBag (eg CarrotSeedBag, CarrotSeedBagIcon). Here are plants:
<orderedlist of plants>
carrot
turnip
pumpkin
potato
onion
strawberry
cauliflower
tomato
parsnip
snap peas
garlic
radish
corn
leek
wheat
unflower
beetroot
cabage
red cabage
broccoli
brussels sprout
red bell pepper
spinach
bok choy
artichoke
cotton
purple grapes
green grapes
red grapes
pink grapes
cantaloupe
honeydew
butternut squash
buckwheat
yellow bell pepper
orange bell pepper
purple bell pepper
white bell pepper
coffee
amaranth
glass gem corn
green chilli pepper
red chilli pepper
yellow chillo pepper
orange chilli pepper
purple chilli pepper
</ordered list of plants>
I will set these sprites in texture manager.
2. I created plant.tscn. It has:
Ground - sprite2d
Plant - sprite2d
PlayerDetector (area2d)
PlayerDetector->CollisionShape2D
ProgressBar
ReadyPlant - sprite2d
AnimationPlayer - AnimationPlayer
So the logic should be as follow:
* add some enum for all these plants so that it will be easier to operate on them
* when player uses hoe to hit a ground then if there is NONE object type on that tile AND there is a flag can plant (these are take from tilemap layers) - then you need to spawn a plant (plant.tscn). Initially you need to hide Plant,Progress bar, and ready plant. Only Ground should be visible.
* Ground should have 4 states: when player prepared ground - so its hit with hoe only, then it can have state when seed is planted but ground is not watered yet (player can use watering can to water field), then it can have a state when it's watered but seed not planted yet and finally when seed is planted and watered. I will provide you images for all 4 sprites - use export Texture2D for all 4.
* when plant is planted and watered then it can start grow. so for grow time calculation you need to add grow time in seconds (double) and subtract time in _Process method. Plant has 5 states of growth. in Plant sprite i already assigned sprite sheet of all plants - so we have 48 rows, each has 5 states. rows are ordered in the same order as <orderedlist of plants>. column 0 only contains seed. So when it's planted - set this seed column and make Plant visible. When it's planted and watered then set column 1. From column 1 to 4 we have 4 states of growth - so adjust them accordingly to the time remaining. Each crop grows for 4 minutes.
* when growing stage ends - play animation "Ready" from AnimationPlayer - this will show additional animation that plant is ready (i will prepare animation). When plant ready, hide progress bar.
* when plant is ready and player is in PlayerDetector range (read Player controller to see which layer should be set to be detected by PlayerDetector - player scene has also PlayerDetector - verify which layer is used there and set appropriate here) and player clicks R then you need to harvest the plant. Harvesting means spawning proper crop type (use DroppedResource). Each crop produces 2 items.
* hoe will have levels. Remember the level of hoe that was used to prepare ground. When plant collected (actually, spawned by DroppedResource), increase a chance of getting additional item by hoe level. So if hoe level 1 then it has 0% chance of getting additional item, when level 2 - 10%, level 3 -20% etc.
* remember level of watering can used also, it will increase grow speed by 5% each level (starting from 0% for level 1, then 5% for level 2 etc).
* when player collects plant and it spawns collectable items then free this tile (i mean set object type to none etc to be able to place something else there) and then queue free.
* When player uses a pickaxe on a tile with plant - it should be instantly removed (and tile data should be cleaned so that i can place there other object - set object type to none) then queue free.
* if given field does not have a plant planted there for 5 minutes then it should be cleaned (set object type to none) and queue free. Player needs to hoe again.
* All data (including hoe and watering can levels, plant type growing, ground state) should be saved and loaded when game closes/opens.