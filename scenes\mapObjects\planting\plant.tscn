[gd_scene load_steps=14 format=3 uid="uid://2duciswm4agd"]

[ext_resource type="Texture2D" uid="uid://d2foipga3wwr0" path="res://resources/solaria/planting/ground_prepared_seeded.png" id="1_ejssu"]
[ext_resource type="Script" uid="uid://dgvyqjj1uu303" path="res://scenes/mapObjects/planting/Plant.cs" id="1_plant_script"]
[ext_resource type="Texture2D" uid="uid://ca8d0iyi4fely" path="res://resources/solaria/planting/plant_growing_stages.png" id="2_lode1"]
[ext_resource type="Texture2D" uid="uid://dhaly0fg4oe2u" path="res://resources/solaria/planting/ground_prepared.png" id="2_nvugm"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_jsxeq"]
[ext_resource type="Texture2D" uid="uid://bsr271jhq50ei" path="res://resources/solaria/UI/progress/progressFrontGreenHorizontal.png" id="4_1vq43"]
[ext_resource type="Texture2D" uid="uid://caoq8yu2qm5xk" path="res://resources/solaria/planting/ground_watered.png" id="4_aprf0"]
[ext_resource type="Texture2D" uid="uid://ce0skvsujihbs" path="res://resources/solaria/planting/ground_watered_seeded.png" id="5_3jui3"]
[ext_resource type="Texture2D" uid="uid://rems8vvttskp" path="res://resources/modernFarm/animated/Modern_Farm_vfx_Rare_Crop_Glitter_16x16.png" id="9_aprf0"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_b17rq"]
size = Vector2(16, 16)

[sub_resource type="Animation" id="Animation_jsxeq"]
resource_name = "Ready"
length = 0.8
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ReadyPlant:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7),
"transitions": PackedFloat32Array(1, 1, 1, 1, 1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3, 4, 5, 6, 7]
}

[sub_resource type="Animation" id="Animation_3jui3"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ReadyPlant:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [1]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1vq43"]
_data = {
&"RESET": SubResource("Animation_3jui3"),
&"Ready": SubResource("Animation_jsxeq")
}

[node name="Plant" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_plant_script")
GroundPreparedTexture = ExtResource("2_nvugm")
GroundSeededNotWateredTexture = ExtResource("1_ejssu")
GroundWateredNotSeededTexture = ExtResource("4_aprf0")
GroundSeededAndWateredTexture = ExtResource("5_3jui3")

[node name="Ground" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -40)
texture = ExtResource("1_ejssu")
offset = Vector2(0, 40)

[node name="Plant" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(0, -8)
texture = ExtResource("2_lode1")
hframes = 5
vframes = 48

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
shape = SubResource("RectangleShape2D_b17rq")

[node name="ProgressBar" parent="." instance=ExtResource("3_jsxeq")]
position = Vector2(0, 7)
scale = Vector2(1, 0.4)
FrontTexture = ExtResource("4_1vq43")

[node name="ReadyPlant" type="Sprite2D" parent="."]
texture = ExtResource("9_aprf0")
hframes = 8
frame = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_1vq43")
}
