using Godot;
using System;

/// <summary>
/// Save data structure for enemies
/// </summary>
[System.Serializable]
public class EnemySaveData
{
    public Vector2 Position { get; set; }
    public int Health { get; set; }
    public int AssignedRegion { get; set; }
    public EnemyType EnemyType { get; set; }
    public EnemyState CurrentState { get; set; }
    public Vector2 TerritoryCenter { get; set; }
    public float TerritoryRadius { get; set; }
    public bool IsAggressive { get; set; }
    public float LastAttackTime { get; set; }
    public bool WasHitByPlayer { get; set; }
    public Vector2 LastKnownPlayerPosition { get; set; }
    public float StateTimer { get; set; }
    
    // Movement data
    public Vector2 StartPosition { get; set; }
    public Vector2 TargetPosition { get; set; }
    public float MovementProgress { get; set; }
    
    // AI state data
    public float LastPatrolTime { get; set; }
    public int PatrolPointIndex { get; set; }
}
