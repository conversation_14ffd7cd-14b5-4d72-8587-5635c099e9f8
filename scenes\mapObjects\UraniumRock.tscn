[gd_scene load_steps=7 format=3 uid="uid://7sbag75d4nuw"]

[ext_resource type="Script" uid="uid://c4qdkntk1sna4" path="res://scenes/mapObjects/UraniumRock.cs" id="1_uranium_rock"]
[ext_resource type="AudioStream" uid="uid://c7wmjx041h8iv" path="res://resources/audio/ovani/Rock Dirt Impact Dull D.ogg" id="2_pglhu"]
[ext_resource type="Texture2D" uid="uid://coafcxji543uk" path="res://resources/solaria/exterior/uraniumStone.png" id="2_rexnb"]
[ext_resource type="AudioStream" uid="uid://46yex8wh4k8c" path="res://resources/audio/ovani/Chisel C.ogg" id="3_ns3d1"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]
[ext_resource type="PackedScene" uid="uid://brwmy8gpv2x3p" path="res://scenes/Audio/EffectPlayer.tscn" id="6_wx36a"]

[node name="UraniumRock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_uranium_rock")
RockDestroyedAudio = ExtResource("2_pglhu")
RockHit = ExtResource("3_ns3d1")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_rexnb")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 5, 5, -5, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)

[node name="EffectPlayer" parent="." instance=ExtResource("6_wx36a")]
