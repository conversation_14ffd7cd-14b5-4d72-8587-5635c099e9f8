Implement tasks:

TASK-0:
Fix Grindstone and Grindstone menu - menu is not opening, when i hit with hammer/pickaxe -hp is not taken/restored. It should work similar to Anvil. You can also use how_to_implement_buildings_new.md as a guide that you created.

TASK-1:
Furnace 1-4 takes 2 spaces width, 2 spaces height. For height (the building is 4 tiles height) - it occupies 2 bottom tiles so that no other building or destroyable object can be spawned there. Acually i want to change it to occupy 3 bottom tiles, not only 2 like now - adjust.

TASK-2:
Make sure that when i build furnace then nothing can spawn on it - no other building, no destroyable object. Currently i had situation that tree or stone spawned on furnace place.

TASK-3:
Furnace 1-4 - when player is behind them they should get semi-translarent - the same like happens for a Tree (destroyable object).

TASK-4:
We want to add a seed making machine (building) called SeedMaker. It will take 1 width tile and 2 height tile. I want you to:
* add a new resource types with icons (TextureManager, ResourceType, ItemInformation, translations): BaseSeed and SeedBag. I will set texutre in texture manager.
* duplicate Grindstone (both, cs and tscn). Then duplicate it's menu (both cs and tscn). Don't create new menu just duplicate it! Then rename it (and nodes) accordingly.
* in Grindstone menu - change item list to have items in following logic: 
1. we have 46 plant types (see plant type enum for order reference). 
2. For each i want to have an option to craft a seed bag so for each seed bag create a separate item list (duplicate! it should look the same as from grindstone that you duplicated!). 
3. For each seed bag the requirements are (order like in PlantType): 1 seed bag + 1 basic seed. 
4. At the begining only first seed bag is unlocked (for carrot). 
5. In order to unlock next seed bag player needs to craft 5 amount of previous one. so to unlock turnip seed bag player needs to craft 5 carrot seed bags. 
6. When seed bag is not unlocked then it should hidden and when unlocked - visible. 
7. As a last item - add an item list that tells "Craft X more Y seed bags to unlock Z seed bag" -> for each we need translation, so use placeholder in label. When all seed bags are unlocked - then hide this node with Craft... information. This last node description should be updated accordingly when unlocked/crafted given plant type.
* you have instruction of how to add a building in how_to_implement_buildings_new.md - use it.
* seed maker should cost 10 stone bricks and 15 planks.