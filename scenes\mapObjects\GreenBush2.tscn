[gd_scene load_steps=4 format=3 uid="uid://cjr3burcalm2m"]

[ext_resource type="Script" uid="uid://cngg3nw0oj7nv" path="res://scenes/mapObjects/GreenBush2.cs" id="1_greenbush2_script"]
[ext_resource type="Texture2D" uid="uid://bi1v0jg6yal2w" path="res://resources/solaria/exterior/Bush2.png" id="2_knj8f"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_greenbush2_progressbar"]

[node name="GreenBush2" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_greenbush2_script")
MaxHealth = 2

[node name="GreenBushSprite" type="Sprite2D" parent="."]
texture = ExtResource("2_knj8f")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(8, 3, 5, 5, -5, 5, -8, 1, -8, -1, 8, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_greenbush2_progressbar")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
