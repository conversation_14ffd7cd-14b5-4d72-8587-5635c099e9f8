using Godot;

public partial class Furnace2Menu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _goldBarSelectButton;
	private Button _indigosiumBarSelectButton;
	private Button _goldKeySelectButton;
	private Button _indigosiumKeySelectButton;

	// InfoBoard controls
	private Sprite2D _itemFront;
	private Label _amountToProduce;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;

	private Furnace2 _currentFurnace;
	private ResourceType _selectedResource = ResourceType.None;
	private int _selectedAmount = 0;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_goldBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCopperBar/Button");
		_indigosiumBarSelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListIronBar/Button");
		_goldKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListGoldKey/Button");
		_indigosiumKeySelectButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListIndigosiumKey/Button");

		// InfoBoard controls
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");

		// Connect signals
		_closeButton.Pressed += OnCloseButtonPressed;
		_goldBarSelectButton.Pressed += OnGoldBarSelectButtonPressed;
		_indigosiumBarSelectButton.Pressed += OnIndigosiumBarSelectButtonPressed;
		_goldKeySelectButton.Pressed += OnGoldKeySelectButtonPressed;
		_indigosiumKeySelectButton.Pressed += OnIndigosiumKeySelectButtonPressed;

		_buttonMinusOne.Pressed += OnButtonMinusOnePressed;
		_buttonPlusOne.Pressed += OnButtonPlusOnePressed;
		_buttonSetOne.Pressed += OnButtonSetOnePressed;
		_buttonSet25Percent.Pressed += OnButtonSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnButtonSet50PercentPressed;
		_buttonSetMax.Pressed += OnButtonSetMaxPressed;
		_buttonProduce.Pressed += OnButtonProducePressed;

		// Set Control/Panel to initially hidden
		var panel = GetNode<Node2D>("Control/Panel");
		if (panel != null)
		{
			panel.Visible = false;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}

		UpdateInfoBoard();
		GD.Print("Furnace2Menu: Ready completed");

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("Furnace2Menu", this);
	}

	public void SetFurnace(Furnace2 furnace)
	{
		_currentFurnace = furnace;
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnGoldBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.GoldBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnIndigosiumBarSelectButtonPressed()
	{
		_selectedResource = ResourceType.IndigosiumBar;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnGoldKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.GoldKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnIndigosiumKeySelectButtonPressed()
	{
		_selectedResource = ResourceType.IndigosiumKey;
		_selectedAmount = 0;
		UpdateInfoBoard();
	}

	private void OnButtonMinusOnePressed()
	{
		if (_selectedAmount > 0)
		{
			_selectedAmount--;
			UpdateInfoBoard();
		}
	}

	private void OnButtonPlusOnePressed()
	{
		if (_selectedAmount < 999)
		{
			_selectedAmount++;
			UpdateInfoBoard();
		}
	}

	private void OnButtonSetOnePressed()
	{
		_selectedAmount = 1;
		UpdateInfoBoard();
	}

	private void OnButtonSet25PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 4) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSet50PercentPressed()
	{
		int maxAffordable = GetMaxAffordableAmount();
		_selectedAmount = maxAffordable > 0 ? Mathf.Max(1, maxAffordable / 2) : 0;
		UpdateInfoBoard();
	}

	private void OnButtonSetMaxPressed()
	{
		_selectedAmount = GetMaxAffordableAmount();
		UpdateInfoBoard();
	}

	private void OnButtonProducePressed()
	{
		if (_currentFurnace == null || _selectedResource == ResourceType.None || _selectedAmount <= 0)
			return;

		if (!_currentFurnace.CanAffordRecipe(_selectedResource, _selectedAmount))
		{
			// Not enough resources, set amount to 0 and update display
			_selectedAmount = 0;
			UpdateInfoBoard();
			return;
		}

		if (_currentFurnace.ConsumeRecipeResources(_selectedResource, _selectedAmount))
		{
			_currentFurnace.StartSmelting(_selectedResource, _selectedAmount);
			CloseMenu();
		}
	}

	private void UpdateInfoBoard()
	{
		if (_selectedResource == ResourceType.None)
		{
			if (_itemFront != null)
			{
				_itemFront.Texture = null;
			}
			if (_amountToProduce != null)
			{
				_amountToProduce.Text = "0";
			}
			if (_buttonProduce != null)
			{
				_buttonProduce.Modulate = new Color(1, 1, 1, 0.5f);
			}
			return;
		}

		// Update item sprite
		if (_itemFront != null)
		{
			var textureManager = TextureManager.Instance;
			if (textureManager != null)
			{
				_itemFront.Texture = textureManager.GetResourceTexture(_selectedResource);
			}
		}

		// Update amount label
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _selectedAmount.ToString();
		}

		// Update produce button state
		if (_buttonProduce != null)
		{
			bool canAfford = _currentFurnace?.CanAffordRecipe(_selectedResource, _selectedAmount) ?? false;
			_buttonProduce.Modulate = canAfford ? Colors.White : new Color(0.6f, 0.6f, 0.6f, 1.0f);
			_buttonProduce.Disabled = !canAfford;
		}
	}

	private int GetMaxAffordableAmount()
	{
		if (_selectedResource == ResourceType.None)
			return 0;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null)
			return 0;

		int maxAmount = 999;
		switch (_selectedResource)
		{
			case ResourceType.GoldBar:
				int goldOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.GoldOre);
				int wood = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(goldOre / 3, wood / 1);
				break;
			case ResourceType.IndigosiumBar:
				int indigosiumOre = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.IndigosiumOre);
				int wood2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Wood);
				maxAmount = Mathf.Min(indigosiumOre / 3, wood2 / 1);
				break;
			case ResourceType.GoldKey:
				int goldBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.GoldBar);
				int charcoal = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(goldBars / 4, charcoal / 1);
				break;
			case ResourceType.IndigosiumKey:
				int indigosiumBars = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.IndigosiumBar);
				int charcoal2 = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Charcoal);
				maxAmount = Mathf.Min(indigosiumBars / 4, charcoal2 / 1);
				break;
		}

		return Mathf.Max(0, maxAmount);
	}

	public void OpenMenu(Furnace2 furnace)
	{
		_currentFurnace = furnace;

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}

		UpdateInfoBoard();
		GD.Print("Furnace2Menu: Menu opened");
	}

	public void CloseMenu()
	{
		_currentFurnace = null;

		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
			// Connect to animation finished to hide panel after close animation
			if (!_animationPlayer.IsConnected(AnimationPlayer.SignalName.AnimationFinished, Callable.From<StringName>(OnCloseAnimationFinished)))
			{
				_animationPlayer.AnimationFinished += OnCloseAnimationFinished;
			}
		}
		else
		{
			// Fallback if no animation player
			GetNode<Node2D>("Control/Panel").Visible = false;
		}

		GD.Print("Furnace2Menu: Menu closed");
	}

	private void OnCloseAnimationFinished(StringName animName)
	{
		if (animName == "Close")
		{
			var panel = GetNode<Node2D>("Control/Panel");
			if (panel != null)
			{
				panel.Visible = false;
			}
			_animationPlayer.AnimationFinished -= OnCloseAnimationFinished;
		}
	}

	// IMenu interface implementation
	void IMenu.OpenMenu()
	{
		// Default implementation - requires furnace to be set externally
		if (_currentFurnace != null)
		{
			OpenMenu(_currentFurnace);
		}
	}

	public bool IsMenuOpen()
	{
		var panel = GetNode<Node2D>("Control/Panel");
		return panel != null && panel.Visible;
	}
}
