using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

public partial class Region1Manager : Node2D
{
	[Export] public float BaseSpawnInterval { get; set; } = 15.0f; // Base 15 seconds
	[Export] public int RegionId { get; set; } = 1;
	[Export] public int InitiallySpawnedObjects { get; set; } = 15;

	// Object spawn probabilities (must add up to 100)
	[Export] public int TreeSpawnChance { get; set; } = 39; // 39% trees (reduced by 1%)
	[Export] public int RockSpawnChance { get; set; } = 24; // 24% rocks (reduced by 1%)
	[Export] public int BerryBushSpawnChance { get; set; } = 20; // 20% berry bushes
	[Export] public int GreenBushSpawnChance { get; set; } = 5; // 5% green bushes
	[Export] public int Rock2SpawnChance { get; set; } = 10; // 10% sandstone rocks
	[Export] public int BrownMushroomSpawnChance { get; set; } = 2; // 2% brown mushrooms

	// Scene references
	[Export] public PackedScene TreeScene { get; set; }
	[Export] public PackedScene RockScene { get; set; }
	[Export] public PackedScene BerryBushScene { get; set; }
	[Export] public PackedScene GreenBushScene { get; set; }
	[Export] public PackedScene Rock2Scene { get; set; }
	[Export] public PackedScene BrownMushroomScene { get; set; }

	// Rabbit spawning configuration
	[Export] public int MaxRabbitsPerRegion { get; set; } = 2;
	[Export] public float RabbitSpawnInterval { get; set; } = 120.0f; // 120 seconds
	[Export] public PackedScene RabbitScene { get; set; }

	private Timer _spawnTimer;
	private Timer _rabbitSpawnTimer;
	private CustomDataLayerManager _customDataManager;
	private Dictionary<Vector2I, Node2D> _activeObjects = new();
	private List<Rabbit> _activeRabbits = new();
	private Dictionary<Vector2I, int> _objectHealthData = new();
	private Random _random = new();

	public override void _Ready()
	{

		if (TreeScene == null)
		{
			TreeScene = GD.Load<PackedScene>("res://scenes/mapObjects/Tree.tscn");
		}

		if (RockScene == null)
		{
			RockScene = GD.Load<PackedScene>("res://scenes/mapObjects/Rock.tscn");
		}

		if (BerryBushScene == null)
		{
			BerryBushScene = GD.Load<PackedScene>("res://scenes/mapObjects/BerryBush.tscn");
		}

		if (GreenBushScene == null)
		{
			GreenBushScene = GD.Load<PackedScene>("res://scenes/mapObjects/GreenBush.tscn");
		}

		if (Rock2Scene == null)
		{
			Rock2Scene = GD.Load<PackedScene>("res://scenes/mapObjects/Rock2.tscn");
		}

		if (BrownMushroomScene == null)
		{
			BrownMushroomScene = GD.Load<PackedScene>("res://scenes/mapObjects/BrownMushroom.tscn");
		}

		if (RabbitScene == null)
		{
			RabbitScene = GD.Load<PackedScene>("res://scenes/mapObjects/animals/Rabbit.tscn");
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Region1Manager: CustomDataLayerManager not found!");
			return;
		}


		_spawnTimer = new Timer();
		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.OneShot = true;
		_spawnTimer.Autostart = true;
		_spawnTimer.Timeout += OnSpawnTimer;
		AddChild(_spawnTimer);

		// Setup rabbit spawn timer (don't autostart - start after loading existing rabbits)
		_rabbitSpawnTimer = new Timer();
		_rabbitSpawnTimer.WaitTime = RabbitSpawnInterval;
		_rabbitSpawnTimer.OneShot = true;
		_rabbitSpawnTimer.Autostart = true;
		_rabbitSpawnTimer.Timeout += OnRabbitSpawnTimer;
		AddChild(_rabbitSpawnTimer);

		CallDeferred(nameof(LoadExistingObjects));
		CallDeferred(nameof(LoadExistingRabbits));
		CallDeferred(nameof(LoadObjectHealthData)); // todo remove

		CallDeferred(nameof(FirstTimeInitialize));
	}

	public void FirstTimeInitialize()
	{
		if(GameSaveData.Instance.FirstTimeInitializedRegions.Contains(RegionId)) return;

		for(var i = 0; i < InitiallySpawnedObjects; i++) TrySpawnObject();
		GameSaveData.Instance.FirstTimeInitializedRegions.Add(RegionId);
	}

	/// <summary>
	/// Timer callback for spawning objects
	/// </summary>
	private void OnSpawnTimer()
	{
		TrySpawnObject();

		_spawnTimer.WaitTime = CalculateNextSpawnInterval();
		_spawnTimer.Start();
	}

	/// <summary>
	/// Timer callback for spawning rabbits
	/// </summary>
	private void OnRabbitSpawnTimer()
	{
		TrySpawnRabbit();

		_rabbitSpawnTimer.WaitTime = RabbitSpawnInterval;
		_rabbitSpawnTimer.Start();
	}

	private float CalculateNextSpawnInterval()
	{
		int objectCount = GetCurrentResourceCount();
		float nextInterval = BaseSpawnInterval + objectCount;

		GD.Print($"Region{RegionId}Manager: Next spawn in {nextInterval}s (base: {BaseSpawnInterval}s + {objectCount} active objects)");
		return nextInterval;
	}

	private int GetCurrentResourceCount()
	{
		// _activeObjects should only contain resource objects from this region
		// due to filtering in LoadExistingObjects and SpawnObjectAt
		int count = _activeObjects.Count;
		GD.Print($"Region{RegionId}Manager: Counting {count} resource objects in region {RegionId}");
		return count;
	}

	/// <summary>
	/// Attempt to spawn an object at a random valid location
	/// </summary>
	private void TrySpawnObject()
	{
		var validPositions = GetValidSpawnPositions();

		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid spawn positions available");
			return; // No valid positions available
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		// Double-check the selected position is in the correct region
		var tileData = _customDataManager.GetTileData(spawnPosition);
		if (tileData.Region != RegionId)
		{
			GD.PrintErr($"Region{RegionId}Manager: ERROR - Selected spawn position {spawnPosition} is in region {tileData.Region}, not {RegionId}!");
			return;
		}

		var objectType = DetermineObjectTypeToSpawn();
		
		// if we didnt unlock region 4 - spawn only tree and stone and bush
		if(!GameSaveData.Instance.UnlockedRegions.Contains(4))
		{
			if(!(objectType == ObjectType.Rock || objectType == ObjectType.Tree || objectType == ObjectType.BerryBush))
			{
				TrySpawnObject();
				return;
			}
		}
		
		SpawnObjectAt(spawnPosition, objectType);
	}

	/// <summary>
	/// Get all valid positions where objects can spawn in region 1
	/// </summary>
	private List<Vector2I> GetValidSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		var spawnableTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject &&
			tile.ObjectTypePlaced == ObjectTypePlaced.None);

		foreach (var tile in spawnableTiles)
		{
			if (!_activeObjects.ContainsKey(tile.Position))
			{
				validPositions.Add(tile.Position);
			}
		}

		GD.Print($"Region{RegionId}Manager: Found {validPositions.Count} valid spawn positions in region {RegionId}");
		return validPositions;
	}
	
	private ObjectType DetermineObjectTypeToSpawn()
	{
		int randomValue = _random.Next(1, 101);

		if (randomValue <= TreeSpawnChance)
		{
			return ObjectType.Tree;
		}
		else if (randomValue <= TreeSpawnChance + RockSpawnChance)
		{
			return ObjectType.Rock;
		}
		else if (randomValue <= TreeSpawnChance + RockSpawnChance + BerryBushSpawnChance)
		{
			return ObjectType.BerryBush;
		}
		else if (randomValue <= TreeSpawnChance + RockSpawnChance + BerryBushSpawnChance + GreenBushSpawnChance)
		{
			return ObjectType.GreenBush;
		}
		else if (randomValue <= TreeSpawnChance + RockSpawnChance + BerryBushSpawnChance + GreenBushSpawnChance + Rock2SpawnChance)
		{
			return ObjectType.Rock2;
		}
		else if (randomValue <= TreeSpawnChance + RockSpawnChance + BerryBushSpawnChance + GreenBushSpawnChance + Rock2SpawnChance + BrownMushroomSpawnChance)
		{
			return ObjectType.BrownMushroom;
		}

		return ObjectType.Tree;
	}

	private void SpawnObjectAt(Vector2I tilePosition, ObjectType objectType)
	{
		// Validate that we're spawning in the correct region
		var tileData = _customDataManager.GetTileData(tilePosition);
		if (tileData.Region != RegionId)
		{
			GD.PrintErr($"Region{RegionId}Manager: ERROR - Attempting to spawn {objectType} at {tilePosition} but tile is in region {tileData.Region}!");
			return;
		}

		GD.Print($"Region{RegionId}Manager: Spawning {objectType} at {tilePosition} (Region: {tileData.Region})");

		Node2D objectInstance = null;

		switch (objectType)
		{
			case ObjectType.Tree:
				objectInstance = SpawnTree(tilePosition);
				break;
			case ObjectType.Rock:
				objectInstance = SpawnRock(tilePosition);
				break;
			case ObjectType.BerryBush:
				objectInstance = SpawnBerryBush(tilePosition);
				break;
			case ObjectType.GreenBush:
				objectInstance = SpawnGreenBush(tilePosition);
				break;
			case ObjectType.Rock2:
				objectInstance = SpawnRock2(tilePosition);
				break;
			case ObjectType.BrownMushroom:
				objectInstance = SpawnBrownMushroom(tilePosition);
				break;
			default:
				GD.PrintErr($"Region1Manager: Unknown object type {objectType}");
				return;
		}

		if (objectInstance != null)
		{
			// Track object
			_activeObjects[tilePosition] = objectInstance;
			UpdateObjectHealthInGameData();

			// Mark tile as occupied
			ObjectTypePlaced placedType = objectType switch
			{
				ObjectType.Tree => ObjectTypePlaced.Tree,
				ObjectType.Rock => ObjectTypePlaced.Rock,
				ObjectType.BerryBush => ObjectTypePlaced.BerryBush,
				ObjectType.GreenBush => ObjectTypePlaced.BerryBush, // Use same placement type as BerryBush
				ObjectType.Rock2 => ObjectTypePlaced.Rock2,
				ObjectType.BrownMushroom => ObjectTypePlaced.BerryBush, // Use same placement type as BerryBush
				_ => ObjectTypePlaced.None
			};
			_customDataManager.SetObjectPlaced(tilePosition, placedType);

			// Save the updated custom layer data immediately
			SaveCustomLayerDataToGameData();
		}
	}

	private Tree SpawnTree(Vector2I tilePosition)
	{
		if (TreeScene == null)
		{
			GD.PrintErr("Region1Manager: TreeScene is null!");
			return null;
		}

		var tree = TreeScene.Instantiate<Tree>();
		if (tree == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate tree!");
			return null;
		}

		tree.SetTilePosition(tilePosition);

		GetParent().CallDeferred("add_child", tree);

		tree.TreeDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, tree);

		return tree;
	}

	private Rock SpawnRock(Vector2I tilePosition)
	{
		if (RockScene == null)
		{
			GD.PrintErr("Region1Manager: RockScene is null!");
			return null;
		}

		// Create rock instance
		var rock = RockScene.Instantiate<Rock>();
		if (rock == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate rock!");
			return null;
		}

		rock.SetTilePosition(tilePosition);

		GetParent().CallDeferred("add_child", rock);

		rock.RockDestroyed += OnObjectDestroyed;

		RestoreObjectHealth(tilePosition, rock);

		return rock;
	}

	private BerryBush SpawnBerryBush(Vector2I tilePosition)
	{
		if (BerryBushScene == null)
		{
			GD.PrintErr("Region1Manager: BerryBushScene is null!");
			return null;
		}

		var berryBush = BerryBushScene.Instantiate<BerryBush>();
		if (berryBush == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate berry bush!");
			return null;
		}

		berryBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", berryBush);
		berryBush.BerryBushDestroyed += OnObjectDestroyed;

		// Restore health if saved data exists
		RestoreObjectHealth(tilePosition, berryBush);

		return berryBush;
	}

	/// <summary>
	/// Spawn a green bush at the specified position
	/// </summary>
	private GreenBush SpawnGreenBush(Vector2I tilePosition)
	{
		if (GreenBushScene == null)
		{
			GD.PrintErr("Region1Manager: GreenBushScene is null!");
			return null;
		}

		var greenBush = GreenBushScene.Instantiate<GreenBush>();
		if (greenBush == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate green bush!");
			return null;
		}

		greenBush.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", greenBush);
		greenBush.GreenBushDestroyed += OnObjectDestroyed;

		// Restore health if saved data exists
		RestoreObjectHealth(tilePosition, greenBush);

		return greenBush;
	}

	/// <summary>
	/// Spawn a sandstone rock at the specified position
	/// </summary>
	private Rock2 SpawnRock2(Vector2I tilePosition)
	{
		if (Rock2Scene == null)
		{
			GD.PrintErr("Region1Manager: Rock2Scene is null!");
			return null;
		}

		var rock2 = Rock2Scene.Instantiate<Rock2>();
		if (rock2 == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate sandstone rock!");
			return null;
		}

		rock2.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", rock2);
		rock2.Rock2Destroyed += OnObjectDestroyed;

		// Restore health if saved data exists
		RestoreObjectHealth(tilePosition, rock2);

		return rock2;
	}

	/// <summary>
	/// Spawn a brown mushroom at the specified position
	/// </summary>
	private BrownMushroom SpawnBrownMushroom(Vector2I tilePosition)
	{
		if (BrownMushroomScene == null)
		{
			GD.PrintErr("Region1Manager: BrownMushroomScene is null!");
			return null;
		}

		var brownMushroom = BrownMushroomScene.Instantiate<BrownMushroom>();
		if (brownMushroom == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate brown mushroom!");
			return null;
		}

		brownMushroom.SetTilePosition(tilePosition);
		GetParent().CallDeferred("add_child", brownMushroom);
		brownMushroom.BrownMushroomDestroyed += OnObjectDestroyed;

		// Restore health if saved data exists
		RestoreObjectHealth(tilePosition, brownMushroom);

		return brownMushroom;
	}

	/// <summary>
	/// Try to spawn a rabbit if under the limit
	/// </summary>
	private void TrySpawnRabbit()
	{
		// Clean up dead rabbits first
		int removedCount = _activeRabbits.RemoveAll(rabbit => !IsInstanceValid(rabbit));
		if (removedCount > 0)
		{
			UpdateRabbitsInGameData();
		}

		// Check if we're under the limit
		if (_activeRabbits.Count >= MaxRabbitsPerRegion)
		{
			GD.Print($"Region{RegionId}Manager: Rabbit limit reached ({_activeRabbits.Count}/{MaxRabbitsPerRegion})");
			return;
		}

		// Find a random valid position for rabbit spawning
		var validPositions = GetValidRabbitSpawnPositions();
		if (validPositions.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No valid rabbit spawn positions available");
			return;
		}

		var randomIndex = _random.Next(validPositions.Count);
		var spawnPosition = validPositions[randomIndex];

		SpawnRabbitAt(spawnPosition);
	}

	/// <summary>
	/// Get valid positions for rabbit spawning (any grass tile in region)
	/// </summary>
	private List<Vector2I> GetValidRabbitSpawnPositions()
	{
		var validPositions = new List<Vector2I>();

		// Rabbits can spawn on any grass tile in the region (they don't lock tiles)
		var grassTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.CanDestroyableObject); // Grass tiles where objects can be placed

		foreach (var tile in grassTiles)
		{
			validPositions.Add(tile.Position);
		}

		GD.Print($"Region{RegionId}Manager: Found {validPositions.Count} valid rabbit spawn positions");
		return validPositions;
	}

	/// <summary>
	/// Spawn a rabbit at the specified position
	/// </summary>
	private void SpawnRabbitAt(Vector2I tilePosition)
	{
		if (RabbitScene == null)
		{
			GD.PrintErr("Region1Manager: RabbitScene is null!");
			return;
		}

		var rabbit = RabbitScene.Instantiate<Rabbit>();
		if (rabbit == null)
		{
			GD.PrintErr("Region1Manager: Failed to instantiate rabbit!");
			return;
		}

		// Set rabbit properties
		rabbit.SetRegion(RegionId);
		rabbit.SetTilePosition(tilePosition);

		// Add to scene
		GetParent().CallDeferred("add_child", rabbit);

		// Track the rabbit
		_activeRabbits.Add(rabbit);
		UpdateRabbitsInGameData();

		GD.Print($"Region{RegionId}Manager: Spawned rabbit at {tilePosition} (Total: {_activeRabbits.Count}/{MaxRabbitsPerRegion})");
	}

	/// <summary>
	/// Handle object destruction
	/// </summary>
	private void OnObjectDestroyed(Vector2I tilePosition)
	{
		if (_activeObjects.ContainsKey(tilePosition))
		{
			_activeObjects.Remove(tilePosition);
			UpdateObjectHealthInGameData();
		}

		// Clear tile occupation (object script should handle this, but double-check)
		_customDataManager.ClearObjectPlaced(tilePosition);

		// Save the updated custom layer data immediately
		SaveCustomLayerDataToGameData();
	}

	private void SaveCustomLayerDataToGameData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null && _customDataManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}
	}

	/// <summary>
	/// Load existing objects from save data
	/// </summary>
	private void LoadExistingObjects()
	{
		// Clear any existing objects first to prevent conflicts
		_activeObjects.Clear();

		// Get all tiles in this specific region with resource objects placed (exclude buildings)
		var objectTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.ObjectTypePlaced != ObjectTypePlaced.None &&
			tile.ObjectTypePlaced != ObjectTypePlaced.Building);

		GD.Print($"Region{RegionId}Manager: Loading {objectTiles.Count} existing resource objects for region {RegionId}");

		int loadedCount = 0;
		int skippedCount = 0;

		foreach (var tile in objectTiles)
		{
			// Validate coordinates are reasonable (prevent corrupted save data)
			if (Math.Abs(tile.Position.X) > 10000 || Math.Abs(tile.Position.Y) > 10000)
			{
				GD.PrintErr($"Region1Manager: Skipping object at {tile.Position} - coordinates are invalid/corrupted");
				_customDataManager.ClearObjectPlaced(tile.Position);
				skippedCount++;
				continue;
			}

			// Validate tile before spawning
			if (!tile.CanDestroyableObject)
			{
				GD.PrintErr($"Region1Manager: Skipping object at {tile.Position} - tile doesn't allow destroyable objects");
				// Clear invalid object data
				_customDataManager.ClearObjectPlaced(tile.Position);
				skippedCount++;
				continue;
			}

			// Check if we already have an object here
			if (_activeObjects.ContainsKey(tile.Position))
			{
				GD.PrintErr($"Region1Manager: Skipping object at {tile.Position} - object already exists in _activeObjects");
				skippedCount++;
				continue;
			}

			// ADDITIONAL CHECK: Look for existing object nodes at this position to prevent visual duplicates
			var existingTrees = GetTree().GetNodesInGroup("trees");
			var existingRocks = GetTree().GetNodesInGroup("rocks");
			var existingBushes = GetTree().GetNodesInGroup("bushes");
			bool hasObjectAtPosition = false;

			foreach (Node node in existingTrees)
			{
				if (node is Tree tree && tree.GetTilePosition() == tile.Position)
				{
					hasObjectAtPosition = true;
					break;
				}
			}

			if (!hasObjectAtPosition)
			{
				foreach (Node node in existingRocks)
				{
					if (node is Rock rock && rock.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
					if (node is Rock2 rock2 && rock2.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
				}
			}

			if (!hasObjectAtPosition)
			{
				foreach (Node node in existingBushes)
				{
					if (node is BerryBush berryBush && berryBush.GetTilePosition() == tile.Position)
					{
						hasObjectAtPosition = true;
						break;
					}
				}
			}

			if (hasObjectAtPosition)
			{
				skippedCount++;
				continue;
			}

			// Convert ObjectTypePlaced to ObjectType for spawning
			ObjectType objectType = tile.ObjectTypePlaced switch
			{
				ObjectTypePlaced.Tree => ObjectType.Tree,
				ObjectTypePlaced.Rock => ObjectType.Rock,
				ObjectTypePlaced.BerryBush => ObjectType.BerryBush,
				ObjectTypePlaced.Rock2 => ObjectType.Rock2,
				ObjectTypePlaced.Building => ObjectType.Anvil, // Default building type
				_ => ObjectType.Empty
			};

			// Skip invalid or empty types
			if (objectType == ObjectType.Empty)
			{
				skippedCount++;
				continue;
			}

			// Skip buildings - they should be handled by BuildingManager
			if (objectType.IsBuilding())
			{
				skippedCount++;
				continue;
			}

			GD.Print($"Region{RegionId}Manager: Loading {objectType} from save at {tile.Position} (Region: {tile.Region})");
			SpawnObjectAt(tile.Position, objectType);
			loadedCount++;
		}

		GD.Print($"Region{RegionId}Manager: Loaded {loadedCount} resource objects, skipped {skippedCount} invalid objects");

		// Debug what objects are being tracked
		DebugActiveObjects();
	}

	/// <summary>
	/// Get object at specific tile position
	/// </summary>
	public Node2D GetObjectAt(Vector2I tilePosition)
	{
		return _activeObjects.TryGetValue(tilePosition, out var obj) ? obj : null;
	}

	/// <summary>
	/// Get all active objects in this region
	/// </summary>
	public Dictionary<Vector2I, Node2D> GetAllObjects()
	{
		return new Dictionary<Vector2I, Node2D>(_activeObjects);
	}

	/// <summary>
	/// Force spawn an object at specific position (for testing/debugging)
	/// </summary>
	public bool ForceSpawnAt(Vector2I tilePosition, ObjectType objectType = ObjectType.Tree)
	{
		var tileData = _customDataManager.GetTileData(tilePosition);

		if (tileData.Region != RegionId || !tileData.CanDestroyableObject || tileData.ObjectTypePlaced != ObjectTypePlaced.None)
		{
			return false; // Invalid position
		}

		if (_activeObjects.ContainsKey(tilePosition))
		{
			return false; // Object already exists
		}

		SpawnObjectAt(tilePosition, objectType);
		return true;
	}

	/// <summary>
	/// Remove object at specific position
	/// </summary>
	public bool RemoveObjectAt(Vector2I tilePosition)
	{
		if (_activeObjects.TryGetValue(tilePosition, out var obj))
		{
			obj.QueueFree();
			_activeObjects.Remove(tilePosition);
			UpdateObjectHealthInGameData();
			_customDataManager.ClearObjectPlaced(tilePosition);
			SaveCustomLayerDataToGameData();
			return true;
		}

		return false;
	}
	
	/// <summary>
	/// Clear all object data for this region (useful for fixing corrupted save data)
	/// </summary>
	public void ClearAllObjectData()
	{
		GD.Print($"Region1Manager: Clearing all object data for region {RegionId}");

		// Remove all active objects
		foreach (var kvp in _activeObjects.ToList())
		{
			kvp.Value.QueueFree();
			_customDataManager.ClearObjectPlaced(kvp.Key);
		}
		_activeObjects.Clear();

		// Clear any remaining object data in the region
		var allObjectTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.ObjectTypePlaced != ObjectTypePlaced.None);

		foreach (var tile in allObjectTiles)
		{
			_customDataManager.ClearObjectPlaced(tile.Position);
		}

		GD.Print($"Region1Manager: Cleared {allObjectTiles.Count} object data entries");
	}

	/// <summary>
	/// Investigate and report on object data corruption (call this to debug save data issues)
	/// </summary>
	public void InvestigateObjectData()
	{
		GD.Print($"=== Region1Manager: Investigating object data for region {RegionId} ===");

		// Get all tiles with object data
		var allObjectTiles = _customDataManager.GetTilesWhere(tile =>
			tile.Region == RegionId &&
			tile.ObjectTypePlaced != ObjectTypePlaced.None);

		GD.Print($"Found {allObjectTiles.Count} tiles with ObjectTypePlaced > 0");

		// Group by object type
		var objectTypeCounts = new Dictionary<ObjectTypePlaced, int>();
		var invalidObjectTypes = new List<Vector2I>();

		foreach (var tile in allObjectTiles)
		{
			if (Enum.IsDefined(typeof(ObjectTypePlaced), tile.ObjectTypePlaced))
			{
				objectTypeCounts[tile.ObjectTypePlaced] = objectTypeCounts.GetValueOrDefault(tile.ObjectTypePlaced, 0) + 1;
			}
			else
			{
				invalidObjectTypes.Add(tile.Position);
			}
		}

		// Report findings
		GD.Print("Object type distribution:");
		foreach (var kvp in objectTypeCounts)
		{
			GD.Print($"  {kvp.Key} (ID: {(int)kvp.Key}): {kvp.Value} tiles");
		}

		if (invalidObjectTypes.Count > 0)
		{
			GD.Print($"Found {invalidObjectTypes.Count} tiles with invalid object types");
		}

		// Check active objects vs data
		GD.Print($"Active objects in memory: {_activeObjects.Count}");
		GD.Print($"Object data in save: {allObjectTiles.Count}");

		// Check for valid spawn positions
		var validSpawnPositions = GetValidSpawnPositions();
		GD.Print($"Valid spawn positions available: {validSpawnPositions.Count}");

		GD.Print("=== Investigation complete ===");
	}

	/// <summary>
	/// Fix corrupted save data by clearing all object data and resetting to clean state
	/// Call this if you have corrupted save data with objects everywhere
	/// </summary>
	public void FixCorruptedSaveData()
	{
		GD.Print($"Region1Manager: Fixing corrupted save data for region {RegionId}");

		// First, investigate what we have
		InvestigateObjectData();

		// Clear all object data
		ClearAllObjectData();

		// Force save the cleaned data
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
			GD.Print("Region1Manager: Saved cleaned data to prevent future corruption");
		}

		GD.Print("Region1Manager: Corruption fix complete. Restart the game to see clean state.");
	}

	/// <summary>
	/// Emergency method to clear all corrupted data and restart clean
	/// Call this from _Ready() if you want to force-clear everything
	/// </summary>
	public void EmergencyClearAllData()
	{
		GD.Print("Region1Manager: EMERGENCY CLEAR - Removing all object data");

		// Remove all active objects
		foreach (var kvp in _activeObjects.ToList())
		{
			kvp.Value.QueueFree();
		}
		_activeObjects.Clear();

		// Clear ALL object data in the entire custom data manager
		var allObjectTiles = _customDataManager.GetTilesWhere(tile => tile.ObjectTypePlaced != ObjectTypePlaced.None);
		foreach (var tile in allObjectTiles)
		{
			_customDataManager.ClearObjectPlaced(tile.Position);
		}

		// Force save immediately
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			resourcesManager.SaveCustomLayerData(_customDataManager);
		}

		GD.Print($"Region1Manager: Emergency clear complete. Cleared {allObjectTiles.Count} object entries.");
	}

	/// <summary>
	/// Debug method to show what objects are currently being tracked
	/// </summary>
	public void DebugActiveObjects()
	{
		GD.Print($"=== Region{RegionId}Manager: Debug Active Objects ===");
		GD.Print($"Total _activeObjects count: {_activeObjects.Count}");

		var objectTypeCounts = new Dictionary<ObjectTypePlaced, int>();

		foreach (var kvp in _activeObjects)
		{
			var tileData = _customDataManager.GetTileData(kvp.Key);
			var objectType = tileData.ObjectTypePlaced;

			objectTypeCounts[objectType] = objectTypeCounts.GetValueOrDefault(objectType, 0) + 1;

			if (objectTypeCounts[objectType] <= 3) // Show first 3 of each type
			{
				GD.Print($"  {objectType} at {kvp.Key} (Region: {tileData.Region})");
			}
		}

		GD.Print("Object type summary:");
		foreach (var kvp in objectTypeCounts)
		{
			GD.Print($"  {kvp.Key}: {kvp.Value} objects");
		}

		GD.Print("=== Debug Complete ===");
	}

	/// <summary>
	/// Load existing rabbits from save data
	/// </summary>
	private void LoadExistingRabbits()
	{
		_activeRabbits.Clear();

		// Load rabbit save data from ResourcesManager
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		var rabbitSaveDataList = resourcesManager.LoadRabbitData(RegionId);
		if (rabbitSaveDataList == null || rabbitSaveDataList.Count == 0)
		{
			GD.Print($"Region{RegionId}Manager: No saved rabbits found for region {RegionId}");
			return;
		}

		GD.Print($"Region{RegionId}Manager: Loading {rabbitSaveDataList.Count} saved rabbits");

		foreach (var saveData in rabbitSaveDataList)
		{
			if (RabbitScene == null) continue;

			var rabbit = RabbitScene.Instantiate<Rabbit>();
			if (rabbit == null) continue;

			// Load rabbit from save data
			rabbit.LoadFromSaveData(saveData);

			// Add to scene and track
			GetParent().CallDeferred("add_child", rabbit);
			_activeRabbits.Add(rabbit);
		}

		GD.Print($"Region{RegionId}Manager: Loaded {_activeRabbits.Count} rabbits for region {RegionId}");

		// Start rabbit spawn timer after loading existing rabbits (only if below max limit)
		if (_rabbitSpawnTimer != null)
		{
			if (_activeRabbits.Count < MaxRabbitsPerRegion)
			{
				_rabbitSpawnTimer.Start();
				GD.Print($"Region{RegionId}Manager: Started rabbit spawn timer ({_activeRabbits.Count}/{MaxRabbitsPerRegion} rabbits)");
			}
			else
			{
				GD.Print($"Region{RegionId}Manager: Max rabbits reached ({_activeRabbits.Count}/{MaxRabbitsPerRegion}), not starting spawn timer");
			}
		}
	}

	/// <summary>
	/// Update rabbit data in GameData
	/// </summary>
	private void UpdateRabbitsInGameData()
	{
		// Clean up dead rabbits first
		_activeRabbits.RemoveAll(rabbit => !IsInstanceValid(rabbit));

		var rabbitSaveDataList = new List<RabbitSaveData>();

		foreach (var rabbit in _activeRabbits)
		{
			if (rabbit.GetRegion() == RegionId)
			{
				rabbitSaveDataList.Add(rabbit.GetSaveData());
			}
		}

		// Update GameData directly
		string key = $"rabbits_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = rabbitSaveDataList;

		GD.Print($"Region{RegionId}Manager: Updated {rabbitSaveDataList.Count} rabbits for region {RegionId} in GameData");
	}

	/// <summary>
	/// Load object health data from save
	/// </summary>
	private void LoadObjectHealthData()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		_objectHealthData = resourcesManager.LoadObjectHealthData(RegionId);
		GD.Print($"Region{RegionId}Manager: Loaded health data for {_objectHealthData.Count} objects");
	}

	/// <summary>
	/// Update object health data in GameData
	/// </summary>
	private void UpdateObjectHealthInGameData()
	{
		// Collect current health from all active objects
		_objectHealthData.Clear();

		foreach (var kvp in _activeObjects)
		{
			var position = kvp.Key;
			var obj = kvp.Value;

			if (obj is IDestroyableObject destroyableObj)
			{
				_objectHealthData[position] = destroyableObj.GetCurrentHealth();
			}
		}

		// Update GameData directly
		string key = $"object_health_region_{RegionId}";
		GameSaveData.Instance.WorldData.CustomLayerData[key] = _objectHealthData;

		GD.Print($"Region{RegionId}Manager: Updated health data for {_objectHealthData.Count} objects in GameData");
	}

	/// <summary>
	/// Restore health to a spawned object if health data exists
	/// </summary>
	private void RestoreObjectHealth(Vector2I position, IDestroyableObject obj)
	{
		if (_objectHealthData.TryGetValue(position, out int savedHealth))
		{
			obj.SetCurrentHealth(savedHealth);
			GD.Print($"Region{RegionId}Manager: Restored health {savedHealth} for object at {position}");
		}
	}
}
