BUGFIX - your task is to fix following bugs, one by one, go to next only when you analyse and fix the previous one:

BUG-1: 
After unlocking region 2 - 15 items should be spawned. This didn't happend - after unlocking region 2 then there were 0 items spawned by Region2Manager.

BUG-2:
When i opened anvil menu from one anvil and selected item to craft then it was selected but for different anvil - not the one from which i opened anvil menu. Fix.

BUG-3:
When there is a chest and i click R in chest range then - if player has a key - the chest should open. The problem is that when i dont have a key and i click R then the player can't move. If i open inventory panel or build panel and i close it then player can move again. Fix so that when i dont have a key and i click R then player can still move.

BUG-4
In region 1 there is a limit to spawn max 2 rabbits - i set it through inspector. But when i close game and open it again there can be spawned more than 2 rabbit. It's in Region1Manager - fix it so that even if i restart game there can be max 2 rabbits in region 1. The same bug is probably in other region managers (region2manager, region3manager and region4manager).

BUG-5
When i open AnvilMenu and i click ESC - then anvil menu is closed. Same should happend for CampfireMenu - but it doesn't. Fix it so that ESC closes campfire menu.

BUG-6
There is a day/night system in game. When i close game and open it again then day/night time is not saved. Fix it so that when i close game and open it again then day/night time is the same as when i closed game.

BUG-7
There is a day/night system in game. I want that when there should be switch to day/night then it should be smooth transition in 5 seconds. Currently it's instatnt and it looks bad.

BUG-8. 
Look at autosave.json - the file that i selected. There are spawned anvils, campfires. When i build anvil/campfire then it should 'block' tile or tiles that it lays on (different tile shapes for diferent objects - for example anvil takes 1 tile height and 2 tiles width). So when on custom layer i have ObjectType different than Empty - then no other building or any other object should be able to spawn on that place. But when I close game and start game and I wait for some time then a tree or berry bush spawned on anvil/campfire. You can analyse autosave.json file to see current data save that has this bug. Fix it so that when the tile (from layer for objectTypePlaced) has value different than Empty then no other object should be spawned on that tile. Maybe some data is incorrectly saved or loaded - i don't know. Maybe data of objectTypePlaced is not properly set/saved? And same for all layers that have some data. Analyse in depth and fix.