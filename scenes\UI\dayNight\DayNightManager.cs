using Godot;

public partial class DayNightManager : CanvasLayer
{
	[Export] public float DayDurationMinutes { get; set; } = 5.0f;
	[Export] public float NightDurationMinutes { get; set; } = 3.0f;
	[Export] public float NightDarknessLevel { get; set; } = 0.3f; // 0.0 = completely dark, 1.0 = no darkness
	[Export] public bool StartAtDawn { get; set; } = true;

	private Sprite2D _dayNightProgress;
	private Label _labelTime;
	private Label _labelAmPm;
	private CanvasModulate _canvasModulate;
	private Tween _lightingTween;

	private float _currentTime = 0.0f; // 0.0 to 1.0 representing full day cycle
	private float _totalCycleDuration;
	private bool _isDay = true;
	private bool _isTransitioning = false;

	// Time display - Start at 8 AM (beginning of day)
	private int _currentHour = 8; // Start at 8 AM
	private int _currentMinute = 0;
	private bool _isAM = true;
	private float _minuteAccumulator = 0.0f; // For fractional minute tracking

	public override void _Ready()
	{
		// Add to group for easy finding
		AddToGroup("DayNightManager");

		_dayNightProgress = GetNode<Sprite2D>("Control/DayNightProgress");
		_labelTime = GetNode<Label>("Control/LabelTime");
		_labelAmPm = GetNode<Label>("Control/LabelAmPm");

		// Create CanvasModulate for global lighting control
		_canvasModulate = new CanvasModulate();
		_canvasModulate.Color = Colors.White; // Start with full brightness
		CallDeferred(MethodName.AddCanvasModulate);

		// Calculate total cycle duration in seconds
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;

		// Load saved time data
		LoadTimeData();

		// Initialize time display
		UpdateTimeDisplay();
		UpdateDayNightVisuals();

		GD.Print($"DayNightManager: Initialized with {DayDurationMinutes}min day, {NightDurationMinutes}min night");
	}

	private void AddCanvasModulate()
	{
		GetTree().CurrentScene.AddChild(_canvasModulate);
		GD.Print("DayNightManager: CanvasModulate added to scene");
	}

	public override void _Process(double delta)
	{
		// Update cycle time
		_currentTime += (float)delta / _totalCycleDuration;
		if (_currentTime >= 1.0f)
		{
			_currentTime -= 1.0f; // Loop the cycle
		}

		// Update game time first (12-hour format)
		UpdateGameTime(delta);

		// Update day/night state based on current time
		// Day: 8:00 AM to 10:00 PM (14 hours)
		// Night: 10:00 PM to 8:00 AM (10 hours)
		bool wasDay = _isDay;
		_isDay = IsCurrentTimeDay();

		// Start smooth transition if day/night state changed
		if (wasDay != _isDay && !_isTransitioning)
		{
			GD.Print($"DayNightManager: Starting transition to {(_isDay ? "Day" : "Night")} at {GetCurrentTimeString()}");
			StartLightingTransition();
		}

		// Update visuals (but not lighting if transitioning)
		UpdateDayNightVisuals();
		UpdateTimeDisplay();

		// Save time data (will be handled by auto-save system)
		SaveTimeData();
	}

	private void UpdateGameTime(double delta)
	{
		// Game time progresses faster than real time
		// Full day cycle (24 hours) = DayDurationMinutes + NightDurationMinutes real minutes
		float gameTimeSpeed = 24.0f * 60.0f / _totalCycleDuration; // game minutes per real second
		float gameMinutesElapsed = (float)delta * gameTimeSpeed;

		// Accumulate fractional minutes
		_minuteAccumulator += gameMinutesElapsed;

		// Process whole minutes
		while (_minuteAccumulator >= 1.0f)
		{
			_minuteAccumulator -= 1.0f;
			_currentMinute++;

			if (_currentMinute >= 60)
			{
				_currentMinute = 0;
				_currentHour++;

				if (_currentHour > 12)
				{
					_currentHour = 1;
					_isAM = !_isAM;
				}
			}
		}
	}

	private bool IsCurrentTimeDay()
	{
		// Day: 8:00 AM to 10:00 PM (14 hours)
		// Night: 10:00 PM to 8:00 AM (10 hours)

		if (_isAM)
		{
			// AM hours: 8, 9, 10, 11, 12 are day
			return _currentHour >= 8 || _currentHour == 12;
		}
		else
		{
			// PM hours: 1, 2, 3, 4, 5, 6, 7, 8, 9 are day, 10, 11, 12 are night
			return _currentHour <= 9;
		}
	}

	private void UpdateDayNightVisuals()
	{
		// Update progress sprite frame (0-5 for different times)
		if (_dayNightProgress != null)
		{
			int frame = Mathf.FloorToInt(_currentTime * 6.0f);
			frame = Mathf.Clamp(frame, 0, 5);
			_dayNightProgress.Frame = frame;
		}

		// Update global lighting only if not transitioning
		if (_canvasModulate != null && !_isTransitioning)
		{
			if (_isDay)
			{
				// Day: full brightness
				_canvasModulate.Color = Colors.White;
			}
			else
			{
				// Night: reduced brightness based on darkness level
				float brightness = NightDarknessLevel;
				_canvasModulate.Color = new Color(brightness, brightness, brightness, 1.0f);
			}
		}
	}

	private void UpdateTimeDisplay()
	{
		if (_labelTime != null)
		{
			_labelTime.Text = $"{_currentHour:D2}:{_currentMinute:D2}";
		}

		if (_labelAmPm != null)
		{
			_labelAmPm.Text = _isAM ? "AM" : "PM";
		}
	}

	// Public methods for other systems
	public bool IsDay()
	{
		return _isDay;
	}

	public bool IsNight()
	{
		return !_isDay;
	}

	public float GetTimeOfDay()
	{
		return _currentTime;
	}

	public string GetCurrentTimeString()
	{
		return $"{_currentHour:D2}:{_currentMinute:D2} {(_isAM ? "AM" : "PM")}";
	}

	public float GetNightDarknessLevel()
	{
		return NightDarknessLevel;
	}

	public void SetDayDuration(float minutes)
	{
		DayDurationMinutes = minutes;
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;
		GD.Print($"DayNightManager: Day duration set to {minutes} minutes");
	}

	public void SetNightDuration(float minutes)
	{
		NightDurationMinutes = minutes;
		_totalCycleDuration = (DayDurationMinutes + NightDurationMinutes) * 60.0f;
		GD.Print($"DayNightManager: Night duration set to {minutes} minutes");
	}

	public void SetNightDarkness(float darkness)
	{
		NightDarknessLevel = Mathf.Clamp(darkness, 0.0f, 1.0f);
		GD.Print($"DayNightManager: Night darkness set to {darkness}");
	}

	// Skip to specific time for testing
	public void SkipToDay()
	{
		_currentHour = 8;
		_currentMinute = 0;
		_isAM = true;
		_minuteAccumulator = 0.0f;
		GD.Print("DayNightManager: Skipped to day (8:00 AM)");
	}

	public void SkipToNight()
	{
		_currentHour = 10;
		_currentMinute = 0;
		_isAM = false;
		_minuteAccumulator = 0.0f;
		GD.Print("DayNightManager: Skipped to night (10:00 PM)");
	}

	private void LoadTimeData()
	{
		var timeData = GameSaveData.Instance.DayNightTimeData;
		if (timeData != null)
		{
			_currentHour = timeData.CurrentHour;
			_currentMinute = timeData.CurrentMinute;
			_isAM = timeData.IsAM;
			_minuteAccumulator = timeData.MinuteAccumulator;
			GD.Print($"DayNightManager: Loaded time data - {GetCurrentTimeString()}");
		}
		else
		{
			GD.Print("DayNightManager: No saved time data found, using default (8:00 AM)");
		}
	}

	private void SaveTimeData()
	{
		var timeData = GameSaveData.Instance.DayNightTimeData;
		timeData.CurrentHour = _currentHour;
		timeData.CurrentMinute = _currentMinute;
		timeData.IsAM = _isAM;
		timeData.MinuteAccumulator = _minuteAccumulator;
	}

	private void StartLightingTransition()
	{
		if (_canvasModulate == null) return;

		_isTransitioning = true;

		// Get current and target colors
		Color currentColor = _canvasModulate.Color;
		Color targetColor;

		if (_isDay)
		{
			// Transitioning to day: full brightness
			targetColor = Colors.White;
		}
		else
		{
			// Transitioning to night: reduced brightness
			float brightness = NightDarknessLevel;
			targetColor = new Color(brightness, brightness, brightness, 1.0f);
		}

		// Create a new tween for this transition
		_lightingTween = CreateTween();
		_lightingTween.TweenProperty(_canvasModulate, "color", targetColor, 5.0f);
		_lightingTween.TweenCallback(Callable.From(OnTransitionComplete));
	}

	private void OnTransitionComplete()
	{
		_isTransitioning = false;
		GD.Print($"DayNightManager: Transition to {(_isDay ? "Day" : "Night")} completed");
	}
}
