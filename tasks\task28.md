1. Now, i want to handle planting. Player needs to be able to assign any seed bag from equipment to quick actions.
2. When player has selected seed bag in quick actions and he looks at tile where he can plant and clicks right mouse button then you should spawn a seed (resouces->solaria->planting->seeds.png) in the center of the player and show seed that move (animate it) to center of tile and it "plants" - which means that the seed should disappear but should start growing of the plant - as described previously in task27.md that you implemented. You can create a scene called SeedMove.tscn and spawn it with parameters source (GlobalPosition of player) and destination (global position of tile center). Design this in clean code and good practice architecture.
3. When player plants a seed (on a tile that can plant, and no plant is growing already there) then you need to remove 1 seed bag from player inventory - then also verify if player has more than 0 seed bags and if no - remove from quick actions and inventory and deselect that position from quick actions.